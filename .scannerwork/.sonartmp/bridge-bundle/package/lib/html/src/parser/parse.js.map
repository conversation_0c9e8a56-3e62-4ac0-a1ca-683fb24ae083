{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../../../../packages/html/src/parser/parse.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,wDAA0C;AAG1C;;;;GAIG;AAEH,MAAM,cAAc,GAAG;IACrB,QAAQ;IACR,iBAAiB;IACjB,wBAAwB;IACxB,wBAAwB;IACxB,0BAA0B;IAC1B,0BAA0B;IAC1B,iBAAiB;IACjB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;CACpB,CAAC;AAEF;;;;GAIG;AACH,SAAgB,SAAS,CAAC,IAAY;IACpC,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,EAAE,CAAC;KACX;IACD,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAiB,EAAE,CAAC;IACrC,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAC5B,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAC1B,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC;QACnC,SAAS,CAAC,IAAY,EAAE,KAAqC;YAC3D,+CAA+C;YAC/C,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,OAAO;aACR;YAED,gDAAgD;YAChD,mCAAmC;YACnC,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;gBACrE,OAAO;aACR;YAED,QAAQ,GAAG,IAAI,CAAC;YAEhB,mBAAmB,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC5C,CAAC;QAED,UAAU,CAAC,IAAY;YACrB,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAClC,OAAO;aACR;YAED,QAAQ,GAAG,KAAK,CAAC;YAEjB,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC;YAEtC,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;gBACxD,IAAI,EAAE,WAAW,CAAC,mBAAmB,EAAE,UAAU,CAAC;gBAClD,MAAM,EAAE,UAAU,CAAC,mBAAmB,EAAE,UAAU,CAAC;gBACnD,MAAM,EAAE,mBAAmB;gBAC3B,UAAU;gBACV,MAAM,EAAE,OAAO;gBACf,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,EAAE;aACX,CAAC,CAAC;YAEH,mBAAmB,GAAG,iBAAiB,CAAC;QAC1C,CAAC;KACF,CAAC,CAAC;IAEH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,OAAO,WAAW,CAAC;AACrB,CAAC;AAtDD,8BAsDC;AAED,SAAS,WAAW,CAAC,MAAc,EAAE,cAAwB;IAC3D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;YAC9B,MAAM;SACP;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,UAAU,CAAC,MAAc,EAAE,cAAwB;IAC1D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;YAC9B,MAAM;SACP;KACF;IACD,OAAO,MAAM,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,aAAa,GAAG,aAAa,CAAC;AAEpC;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,GAAW;IACpC,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACnB,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;IAC5B,OAAO,IAAI,EAAE;QACX,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM;SACP;QACD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KACtC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}