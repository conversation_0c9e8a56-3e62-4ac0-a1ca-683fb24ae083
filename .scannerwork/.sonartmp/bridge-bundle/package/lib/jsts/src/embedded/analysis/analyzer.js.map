{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/embedded/analysis/analyzer.ts"], "names": [], "mappings": ";;;AAqBA,yCAA+D;AAC/D,wCAAkF;AAElF,mDAA8C;AAC9C,iDAAmD;AACnD,+DAAgE;AAEhE;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,SAAgB,eAAe,CAC7B,KAA4B,EAC5B,cAA8B;IAE9B,IAAA,eAAK,EAAC,mBAAmB,KAAK,CAAC,QAAQ,oBAAoB,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;IAC9E,MAAM,MAAM,GAAG,IAAA,kBAAS,EAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC,IAAA,0BAAgB,EAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAC/D,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAA,4BAAe,EAAC,QAAQ,CAAC,CAAC;IACvF,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;IAChE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAA,4BAAe,EAAC,QAAQ,CAAC,CAAC;IAE7E,OAAO;QACL,GAAG,MAAM;QACT,IAAI,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;KAClC,CAAC;AACJ,CAAC;AAfD,0CAeC;AAED;;;;;;GAMG;AACH,SAAS,WAAW,CAAC,MAAqB,EAAE,mBAAyC;IACnF,MAAM,gBAAgB,GAAY,EAAE,CAAC;IACrC,MAAM,mBAAmB,GAAa,EAAE,CAAC;IACzC,IAAI,KAAK,GAAa,EAAE,CAAC;IACzB,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE;QACpD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAC7F,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAClC,MAAM,cAAc,GAAG,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QACrE,gBAAgB,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QACzC,mBAAmB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;KACxC;IACD,OAAO;QACL,MAAM,EAAE,gBAAgB;QACxB,SAAS,EAAE,mBAAmB;QAC9B,OAAO,EAAE,EAAE,KAAK,EAAE;KACnB,CAAC;IAEF,SAAS,cAAc,CAAC,MAAqB,EAAE,kBAAsC;QACnF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,IAAI,CACvC,kBAAkB,EAClB,kBAAkB,CAAC,iBAAiB,EACpC,MAAM,CACP,CAAC;QACF,MAAM,KAAK,GAAG,IAAA,iBAAS,EAAC,kBAAkB,CAAC,CAAC;QAC5C,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACH,SAAS,iBAAiB,CAAC,UAAsB,EAAE,MAAe;QAChE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;QAChG,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,UAAU,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YAC9D,OAAO,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,SAAS,eAAe,CAAC,CAAW,EAAE,CAAW;YAC/C,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE;gBACnB,OAAO,IAAI,CAAC;aACb;iBAAM,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE;gBAC1B,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC;aAC7B;QACH,CAAC;IACH,CAAC;AACH,CAAC"}