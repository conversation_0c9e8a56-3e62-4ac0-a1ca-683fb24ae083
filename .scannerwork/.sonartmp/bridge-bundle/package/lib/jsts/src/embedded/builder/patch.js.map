{"version": 3, "file": "patch.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/embedded/builder/patch.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,mCAAyC;AAEzC,yCAAqC;AAIrC;;;;;;;;;GASG;AACH,SAAgB,eAAe,CAAC,kBAA8B,EAAE,UAAsB;IACpF;;;;OAIG;IACH,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC;IAE7B;;;;OAIG;IACH,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;QAC1D,gBAAgB,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,UAAU,EAAE;QAClD,IAAI,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,IAAI,EAAE;QAChC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;KACxB,CAAC,CAAC;IAEH;;;OAGG;IACH,iBAAiB,CAAC,iBAAiB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IAExD;;;OAGG;IACH,OAAO,IAAI,mBAAU,CAAC;QACpB,IAAI,EAAE,iBAAiB,CAAC,IAAI;QAC5B,GAAG,EAAE,iBAAiB,CAAC,GAAG;QAC1B,cAAc,EAAE,iBAAiB,CAAC,cAAc;QAChD,YAAY,EAAE,iBAAiB,CAAC,YAAY;QAC5C,WAAW,EAAE,iBAAiB,CAAC,WAAW;KAC3C,CAAC,CAAC;IAEH,kEAAkE;IAClE,SAAS,YAAY;QACnB,MAAM,gBAAgB,GAAG,0BAA0B,CAAC;QACpD,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpE,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;YACxD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACzE,CAAC,EAAE,CAAC;SACL;QACD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3F,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,SAAS,iBAAiB,CAAC,UAAsB,EAAE,MAAc;QAC/D,IAAA,cAAK,EAAC,UAAU,EAAE,IAAI,CAAC,EAAE;YACvB,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC;QACpC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,eAAe,CAAC,OAAO,CAAC,CAAC;SAC1B;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC;QAClC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,eAAe,CAAC,KAAK,CAAC,CAAC;SACxB;QAED,SAAS,eAAe,CAAC,IAAgC;YACvD,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;gBAC1C,IAAI,CAAC,GAAG,GAAG;oBACT,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;oBACzD,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;iBACxD,CAAC;aACH;YACD,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBACpC,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;aACjD;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAxFD,0CAwFC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAAC,YAAsB,EAAE,UAAsB;;IAC9E,IAAI,OAAO,CAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,IAAI,CAAA,KAAK,QAAQ,EAAE;QAC/C,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC;QACvC,MAAM,WAAW,GACf,UAAU,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAChF,YAAY,CAAC,OAAO,GAAG,wBAAwB,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAClF,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;KACtC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AATD,8CASC;AAED;;;;;GAKG;AACH,SAAgB,wBAAwB,CACtC,OAAe,EACf,WAAmB,EACnB,UAAsB;IAEtB,mEAAmE;IACnE,MAAM,KAAK,GAAG,+BAA+B,CAAC;IAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACnC,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;QACjB,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;QAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC9F,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI,WAAW,IAAI,aAAa,GAAG,CAAC,CAAC;KACpF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAfD,4DAeC"}