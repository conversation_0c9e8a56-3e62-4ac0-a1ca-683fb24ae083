{"version": 3, "file": "messages.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/quickfixes/messages.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH;;;;;;GAMG;AACH,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAiB;IAC/C,CAAC,cAAc,EAAE,4BAA4B,CAAC;IAC9C,CAAC,UAAU,EAAE,mCAAmC,CAAC;IACjD,CAAC,yBAAyB,EAAE,2BAA2B,CAAC;IACxD,CAAC,oBAAoB,EAAE,yBAAyB,CAAC;IACjD,CAAC,eAAe,EAAE,qBAAqB,CAAC;IACxC,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;IAC9C,CAAC,eAAe,EAAE,wBAAwB,CAAC;IAC3C,CAAC,qBAAqB,EAAE,yBAAyB,CAAC;IAClD,CAAC,cAAc,EAAE,wBAAwB,CAAC;IAC1C,CAAC,uBAAuB,EAAE,qCAAqC,CAAC;IAChE,CAAC,oBAAoB,EAAE,uBAAuB,CAAC;IAC/C,CAAC,eAAe,EAAE,uBAAuB,CAAC;IAC1C,CAAC,+BAA+B,EAAE,sBAAsB,CAAC;IACzD,CAAC,+BAA+B,EAAE,uBAAuB,CAAC;IAC1D,CAAC,qBAAqB,EAAE,mCAAmC,CAAC;IAC5D,CAAC,mBAAmB,EAAE,cAAc,CAAC;IACrC,CAAC,QAAQ,EAAE,0BAA0B,CAAC;IACtC,CAAC,kBAAkB,EAAE,wBAAwB,CAAC;IAC9C,CAAC,iBAAiB,EAAE,yBAAyB,CAAC;IAC9C,CAAC,cAAc,EAAE,sBAAsB,CAAC;IACxC,CAAC,sBAAsB,EAAE,8BAA8B,CAAC;IACxD,CAAC,yBAAyB,EAAE,0BAA0B,CAAC;IACvD,CAAC,0BAA0B,EAAE,kCAAkC,CAAC;IAChE,CAAC,uBAAuB,EAAE,8BAA8B,CAAC;IACzD,CAAC,sBAAsB,EAAE,mCAAmC,CAAC;IAC7D,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,yBAAyB,EAAE,iCAAiC,CAAC;IAC9D,CAAC,iBAAiB,EAAE,sCAAsC,CAAC;IAC3D,CAAC,cAAc,EAAE,2BAA2B,CAAC;IAC7C,CAAC,QAAQ,EAAE,YAAY,CAAC;IACxB,CAAC,OAAO,EAAE,iBAAiB,CAAC;IAC5B,CAAC,MAAM,EAAE,eAAe,CAAC;CAC1B,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,SAAgB,kBAAkB,CAAC,OAAe;IAChD,MAAM,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,KAAK,CAAC,kCAAkC,OAAO,GAAG,CAAC,CAAC;KAC3D;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAND,gDAMC"}