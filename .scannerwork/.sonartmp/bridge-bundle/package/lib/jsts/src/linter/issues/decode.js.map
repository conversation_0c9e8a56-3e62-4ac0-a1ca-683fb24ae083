{"version": 3, "file": "decode.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/issues/decode.ts"], "names": [], "mappings": ";;;AAsBA,8CAAsD;AAEtD;;;;;;;;;;;;;;GAcG;AACH,SAAgB,kBAAkB,CAAC,UAAuC,EAAE,KAAY;IACtF,IAAI,IAAA,kCAAqB,EAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE;QACnD,IAAI;YACF,MAAM,cAAc,GAAmB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACjE,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,cAAc,EAAE,CAAC;SACxC;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CACb,kDAAkD,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,CACpG,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAZD,gDAYC"}