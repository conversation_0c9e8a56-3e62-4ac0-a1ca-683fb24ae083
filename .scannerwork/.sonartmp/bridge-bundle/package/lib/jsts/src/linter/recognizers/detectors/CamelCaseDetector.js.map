{"version": 3, "file": "CamelCaseDetector.js", "sourceRoot": "", "sources": ["../../../../../../packages/jsts/src/linter/recognizers/detectors/CamelCaseDetector.ts"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,2DAAmC;AAEnC,MAAqB,iBAAkB,SAAQ,kBAAQ;IACrD,IAAI,CAAC,IAAY;QACf,IAAI,YAAY,GAAG,GAAG,CAAC;QACvB,IAAI,WAAW,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,wBAAwB,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE;gBACvD,OAAO,CAAC,CAAC;aACV;YACD,YAAY,GAAG,WAAW,CAAC;SAC5B;QACD,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAbD,oCAaC;AAED,SAAS,wBAAwB,CAAC,YAAoB,EAAE,IAAY;IAClE,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IAErD,SAAS,WAAW,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC;IACrC,CAAC;IACD,SAAS,UAAU,CAAC,IAAY;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC;IACrC,CAAC;AACH,CAAC"}