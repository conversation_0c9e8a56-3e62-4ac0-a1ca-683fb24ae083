{"version": 3, "file": "rule-config.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/config/rule-config.ts"], "names": [], "mappings": ";;;AAoBA,mDAA2E;AAC3E,8CAA4F;AAsB5F;;;;;;;;;;;;GAYG;AACH,SAAgB,gBAAgB,CAAC,UAAuC,EAAE,SAAqB;IAC7F,MAAM,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC;IAC9C,IAAI,IAAA,kCAAqB,EAAC,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE;QACpD,OAAO,CAAC,IAAI,CAAC,0BAAa,CAAC,CAAC;KAC7B;IACD,IAAI,IAAA,kCAAqB,EAAC,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE;QACpD,OAAO,CAAC,IAAI,CAAC,IAAA,oBAAU,GAAE,CAAC,CAAC;KAC5B;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AATD,4CASC"}