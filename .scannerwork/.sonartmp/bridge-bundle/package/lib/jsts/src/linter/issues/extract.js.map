{"version": 3, "file": "extract.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/issues/extract.ts"], "names": [], "mappings": ";;;AAoBA,+EAAuF;AACvF,6EAAqF;AAGrF;;;;;;;;;;;GAWG;AACH,SAAgB,yBAAyB,CAAC,MAAe;IACvD,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,EAAE,0BAAsB,CAAC,MAAM,CAAC,CAAC;IAC7E,IAAI,KAAK,EAAE;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAsB,CAAC;KACvD;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAND,8DAMC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,0BAA0B,CAAC,MAAe;IACxD,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,EAAE,2BAAuB,CAAC,MAAM,CAAC,CAAC;IAC9E,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE;QAC1C,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC9B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAND,gEAMC;AAED;;;;;;;;GAQG;AACH,SAAS,uBAAuB,CAAC,MAAe,EAAE,MAAc;IAC9D,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACxB,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}