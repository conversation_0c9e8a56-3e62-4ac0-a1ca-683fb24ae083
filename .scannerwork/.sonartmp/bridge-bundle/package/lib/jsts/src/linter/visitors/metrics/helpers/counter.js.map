{"version": 3, "file": "counter.js", "sourceRoot": "", "sources": ["../../../../../../../packages/jsts/src/linter/visitors/metrics/helpers/counter.ts"], "names": [], "mappings": ";;;AAqBA,8BAA+B;AAE/B;;;;;GAKG;AACH,SAAgB,eAAe,CAC7B,UAAsB,EACtB,SAAyC;IAEzC,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAA,SAAK,EAAC,UAAU,EAAE,IAAI,CAAC,EAAE;QACvB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,EAAE,CAAC;SACX;IACH,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAXD,0CAWC"}