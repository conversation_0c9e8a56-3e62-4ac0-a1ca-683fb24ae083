{"version": 3, "file": "rules.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/quickfixes/rules.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH;;;;;;;;;;;GAWG;AACU,QAAA,aAAa,GAAG,IAAI,GAAG,CAAC;IACnC,cAAc;IACd,cAAc;IACd,UAAU;IACV,eAAe;IACf,uBAAuB;IACvB,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,eAAe;IACf,qBAAqB;IACrB,oBAAoB;IACpB,mBAAmB;IACnB,QAAQ;IACR,kBAAkB;IAClB,cAAc;IACd,uBAAuB;IACvB,uBAAuB;IACvB,iBAAiB;IACjB,QAAQ;IACR,OAAO;IACP,MAAM;IAEN,wBAAwB;IACxB,sBAAsB;IACtB,mBAAmB;IACnB,UAAU;IACV,cAAc;IACd,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;IAChB,iCAAiC;IACjC,iBAAiB;IACjB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe;IACf,WAAW;IAEX,wBAAwB;IACxB,6BAA6B;IAC7B,2BAA2B;IAC3B,mBAAmB;IACnB,uBAAuB;IACvB,yBAAyB;IACzB,8BAA8B;IAC9B,cAAc;IAEd,sBAAsB;IACtB,gBAAgB;IAChB,yBAAyB;IACzB,qBAAqB;IAErB,4BAA4B;IAC5B,iCAAiC;IACjC,oBAAoB;IACpB,iBAAiB;IACjB,qBAAqB;IACrB,uBAAuB;IACvB,+BAA+B;IAC/B,+BAA+B;IAC/B,gCAAgC;IAChC,iBAAiB;IACjB,sBAAsB;IACtB,0BAA0B;IAC1B,2BAA2B;IAC3B,iBAAiB;IACjB,yBAAyB;IACzB,gCAAgC;IAEhC,sCAAsC;IACtC,0BAA0B;IAC1B,eAAe;IAEf,UAAU;IACV,mBAAmB;IACnB,4BAA4B;IAC5B,8BAA8B;IAC9B,sBAAsB;IACtB,mBAAmB;IACnB,2BAA2B;IAC3B,oBAAoB;IACpB,gBAAgB;IAChB,cAAc;IACd,6BAA6B;IAC7B,uBAAuB;IACvB,uBAAuB;IACvB,0BAA0B;IAC1B,uBAAuB;IACvB,mBAAmB;IACnB,6BAA6B;IAC7B,0BAA0B;IAC1B,mBAAmB;IACnB,oBAAoB;IACpB,qCAAqC;IACrC,8BAA8B;IAC9B,0BAA0B;IAC1B,wBAAwB;IACxB,+BAA+B;IAC/B,eAAe;CAChB,CAAC,CAAC"}