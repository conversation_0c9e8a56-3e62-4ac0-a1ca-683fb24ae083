{"version": 3, "file": "EndWithDetector.js", "sourceRoot": "", "sources": ["../../../../../../packages/jsts/src/linter/recognizers/detectors/EndWithDetector.ts"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,2DAAmC;AAEnC,MAAqB,eAAgB,SAAQ,kBAAQ;IAGnD,YAAY,WAAmB,EAAE,GAAG,UAAoB;QACtD,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,IAAY;QACf,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;gBACvC,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,OAAO,CAAC,CAAC;iBACV;aACF;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;gBACvD,OAAO,CAAC,CAAC;aACV;SACF;QACD,OAAO,CAAC,CAAC;QAET,SAAS,YAAY,CAAC,IAAY;YAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;CACF;AA1BD,kCA0BC"}