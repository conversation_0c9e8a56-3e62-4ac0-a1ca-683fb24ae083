{"version": 3, "file": "linter-config.js", "sourceRoot": "", "sources": ["../../../../../packages/jsts/src/linter/config/linter-config.ts"], "names": [], "mappings": ";;;AAoBA,mDAAmD;AACnD,kDAAqE;AACrE,+CAA6D;AAE7D;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,kBAAkB,CAChC,UAAwB,EACxB,WAAyC,EACzC,eAAyB,EAAE,EAC3B,QAAkB,EAAE;IAEpB,MAAM,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IACrC,MAAM,aAAa,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAA0B,CAAC;IAC1F,MAAM,MAAM,GAAkB;QAC5B,GAAG;QACH,OAAO;QACP,aAAa;QACb,KAAK,EAAE,EAAE;QACT,kFAAkF;QAClF,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE;KAChD,CAAC;IACF,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IAC7C,yBAAyB,CAAC,MAAM,CAAC,CAAC;IAClC,OAAO,MAAM,CAAC;AAChB,CAAC;AApBD,gDAoBC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,YAAsB;IACvC,MAAM,GAAG,GAAgC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACvD,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;QAC9B,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;KACjB;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,KAAe;IACpC,MAAM,OAAO,GAAgC,EAAE,CAAC;IAChD,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;QACvB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;KACrB;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAS,WAAW,CAClB,MAAqB,EACrB,UAAwB,EACxB,WAAyC;IAEzC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAClC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClD,MAAM,CAAC,KAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAA,8BAAgB,EAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;KACtF;AACH,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,yBAAyB,CAAC,MAAqB;IACtD,IAAI,CAAC,IAAA,oBAAU,GAAE,CAAC,SAAS,EAAE;QAC3B,KAAK,MAAM,kBAAkB,IAAI,0BAAmB,EAAE;YACpD,MAAM,CAAC,KAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;SACxF;KACF;AACH,CAAC"}