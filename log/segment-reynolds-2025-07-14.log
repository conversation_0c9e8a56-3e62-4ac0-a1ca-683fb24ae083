
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Mon Jul 14 2025 12:02:20 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:20 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:20 GMT+0000 : stdout: Processor status: Processing Started

Mon Jul 14 2025 12:02:21 GMT+0000 : stdout: HALT_OVER_RIDE:false

Mon Jul 14 2025 12:02:21 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip[0m

Mon Jul 14 2025 12:02:21 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:21 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:21 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Mon Jul 14 2025 12:02:22 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp
Mon Jul 14 2025 12:02:22 GMT+0000 : stdout: [0m

Mon Jul 14 2025 12:02:22 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:22 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:22 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Mon Jul 14 2025 12:02:23 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:23 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:23 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Mon Jul 14 2025 12:02:24 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Mon Jul 14 2025 12:02:24 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:24 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:24 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Mon Jul 14 2025 12:02:25 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Mon Jul 14 2025 12:02:25 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Mon Jul 14 2025 12:02:25 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:25 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:25 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Mon Jul 14 2025 12:02:29 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: Mon Jul 14 12:02:29 UTC 2025 Found # 29

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: Mon Jul 14 12:02:29 UTC 2025 Transform Begin

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: RO_COUNT is greater than 1

Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: Mon Jul 14 12:02:29 UTC 2025 Transform End

Mon Jul 14 2025 12:02:29 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:29 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:29 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Mon Jul 14 2025 12:02:30 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Mon Jul 14 2025 12:02:30 GMT+0000 : stdout: Mon Jul 14 12:02:30 UTC 2025

Mon Jul 14 2025 12:02:31 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Mon Jul 14 2025 12:02:42 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:42 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:42 GMT+0000 : stdout: Mon Jul 14 12:02:42 UTC 2025

Mon Jul 14 2025 12:02:42 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Mon Jul 14 2025 12:02:43 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Mon Jul 14 2025 12:02:43 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:43 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:43 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Mon Jul 14 2025 12:02:44 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 12:02:44 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 12:02:44 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:44 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:44 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 12:02:44 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mCreating exclusion reports[0m

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Mon Jul 14 2025 12:02:45 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Mon Jul 14 2025 12:02:45 GMT+0000 : Total Ro90999999999,-    29

Mon Jul 14 2025 12:02:45 GMT+0000 : totalRoCount666666,    29

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: Total Ros Count:-    29

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: im_count:     0
im_exception:     0
im_count less than zero

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117
suffixed_invoices_count:1

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: punch time missing count :34.38 

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117
suffixed_invoices_count_numeric:0

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Mon Jul 14 2025 12:02:45 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Mon Jul 14 2025 12:02:46 GMT+0000 : stdout: [1;36mCreating New Status File!
Mon Jul 14 2025 12:02:46 GMT+0000 : stdout: [0m

Mon Jul 14 2025 12:02:46 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:46 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:46 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Mon Jul 14 2025 12:02:47 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting[0m

Mon Jul 14 2025 12:02:48 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131-REPORT.zip-----

Mon Jul 14 2025 12:02:48 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:48 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:48 GMT+0000 : stdout:   adding: invoice-master.csv
Mon Jul 14 2025 12:02:48 GMT+0000 : stdout:  (deflated 86%)
Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Mon Jul 14 2025 12:02:49 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Mon Jul 14 2025 12:02:49 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:49 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:49 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Mon Jul 14 2025 12:02:49 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout: Processing config for COMPANY_ID: *********
Processing config for COMPANY_ID: *********

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout: [1;36mGenerating Config File[0m
####################                     /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Scanning for configuration files in: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Config directory exists, searching for config_*.bash files...

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Found 3 config files: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_00000000000.bash /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_*********.bash /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_*********.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Examining config file: config_00000000000.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:    Found COMPANY_ID: '00000000000'

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Added COMPANY_ID: 00000000000 from config_00000000000.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Examining config file: config_*********.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:    Found COMPANY_ID: '*********'

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Added COMPANY_ID: ********* from config_*********.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Examining config file: config_*********.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:    Found COMPANY_ID: '*********'

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Added COMPANY_ID: ********* from config_*********.bash

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Total detected company IDs: 3
 Company IDs: 00000000000 ********* *********

Mon Jul 14 2025 12:02:50 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 12:02:50 GMT+0000 : processorStatus
Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Multiple company IDs detected: 3 companies
 Setting COMPANY_IDS='00000000000,*********,*********'

Mon Jul 14 2025 12:02:50 GMT+0000 : stdout:  Setting PERFORM_PROXY_RO_BUILD='true'
 Parallel proxy generation enabled for companies: 00000000000,*********,*********
Processor status: 8/16 Load Fron Scheduler DB Started

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: COPY 49

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: COMPANY_BRAND: NA

Mon Jul 14 2025 12:02:51 GMT+0000 : stderr: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/paytype_details_00000000000.csv: No such file or directory

Mon Jul 14 2025 12:02:51 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip was copied to /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed
Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: INSERT 0 0

Mon Jul 14 2025 12:02:51 GMT+0000 : stderr: [1;31mCOMPANY_BRAND is empty or does not exist[0m

Mon Jul 14 2025 12:02:51 GMT+0000 : stdout: [1;33mMoving input to dead-letter bin: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed[0m

Mon Jul 14 2025 12:02:51 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : stderr: cp: cannot stat '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip': No such file or directory

Mon Jul 14 2025 12:02:51 GMT+0000 : invalidmiscpaytypeArray.length: 17
Mon Jul 14 2025 12:02:51 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 12:02:51 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : estimateArray.length: 94
Mon Jul 14 2025 12:02:51 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 12:02:51 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Mon Jul 14 2025 12:02:51 GMT+0000 : punchTimeMissingCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : suffixedInvoicesCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : exceptionClosedInvoicesCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : imOpenedClosedRciRosCount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Mon Jul 14 2025 12:02:51 GMT+0000 : deletedRoscount: undefined
Mon Jul 14 2025 12:02:51 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 1
Mon Jul 14 2025 12:02:51 GMT+0000 : Autosoft : filePath inpObj Error - QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip
Mon Jul 14 2025 12:02:51 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T12:01:31.002Z\",\"uniqueId\":\"rc20250714120131003566\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip\",\"endTime\":\"2025-07-14T12:01:31.131Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 12:02:51 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - {"inProjectId":[""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T12:01:31.002Z\",\"uniqueId\":\"rc20250714120131003566\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip\",\"endTime\":\"2025-07-14T12:01:31.131Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 12:02:51 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Mon Jul 14 2025 12:02:51 GMT+0000 : Reynolds : doPayloadAction - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T12:01:31.002Z\",\"uniqueId\":\"rc20250714120131003566\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714120131.zip\",\"endTime\":\"2025-07-14T12:01:31.131Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 12:02:51 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Mon Jul 14 2025 12:02:53 GMT+0000 : Call method for SharePoint data upload
Mon Jul 14 2025 12:02:53 GMT+0000 : Call for next job selection
Mon Jul 14 2025 12:02:53 GMT+0000 : Call for next job selection
Mon Jul 14 2025 12:03:18 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Mon Jul 14 2025 12:03:18 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
Mon Jul 14 2025 12:03:41 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Mon Jul 14 2025 12:03:41 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
Mon Jul 14 2025 12:03:48 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Mon Jul 14 2025 12:03:48 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
