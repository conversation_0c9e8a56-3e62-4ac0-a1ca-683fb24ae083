"use strict";

const constants = require("../constants");
const util = require("../util");
const moment = require("moment-timezone");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const segment = require("../../SEGMENT/Reynolds/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
const mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
const Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
const { CHART_OF_ACCOUNTS_FILE_UPLOAD_DIRECTORY } = require("../../DealerTrack/constants");
const { Console } = require("console");
const accounting_csv_directory = '/etl/accounting_in/';
const csv=require('csvtojson');
const unZipper = require("unzipper");
const csvParser = require('csv-parser');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus')
/**
 * Function to perform processing of XML file downloaded through Reynolds-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    const distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, etlDMSType, jobId) {
        let stdErrorArray;
        let distDir = path.join(process.env.REYNOLDS_DIST_DIR, fileName);
        let filePath = path.join(process.env.REYNOLDS_BUNDLE_DIR, fileName);
        let DMSType;
        let ETLDIR;
        
        if(etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase()){
            DMSType = constants.JOB_TYPE; 
            ETLDIR = process.env.REYNOLDS_ETL_DIR;  
        } else{
            DMSType = 'REYNOLDSRCI'; 
            ETLDIR = process.env.REYNOLDS_ETL_DIR;
        }
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath , ETLDIR
            ], {
                cwd: constants.PROCESS_JSON.REYNOLDS_DISTRIBUTE_CMD_PATH,               
                env: {...process.env,PATH: process.env.PATH + ":/usr/local/bin",},
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`Reynolds: Start processing of distribution`);
        segment.saveSegment(`Reynolds:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", (data) => {
            (async () => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
            })().catch((err) => {
                console.error("Error in stdout data processing:", err);
            });
        });

        distributeFile.stderr.on("data", (data) => {
            (async () => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
            })().catch((err) => {
                console.error("Error in stdout data processing:", err);
            });
        });

        distributeFile.on("close", (code) => {
            (async () => {
            let message = "n/a";
            let status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, DMSType, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
            })().catch((err) => {
                console.error("Error in stdout data processing:", err);
            });
        });
    }

    const doNextProcess = async function () {
        const extractZip = await util.findOldestZipFile(constants.REYNOLDS_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
        } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${extractZip}`)) {
            console.log(`Reynolds : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Reynolds : Found one Store extraction > ${extractZip} to process now`);
            try {
                let createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "json-processing"
                });
                console.log(`Reynolds : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`Reynolds : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`Reynolds : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`Reynolds : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `Reynolds : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`Reynolds : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY,lockLifetime: 1 * 60 * 60 * 1000 },
        async (job, done) => {
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);
            const att = job.attrs.data;
            let extractZip = null;
            let stdErrorArray = [];
            let stdOutArray = [];
            let uniqueFailLogName;

            let inpFile = att.inputFile ? path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed')) {
                    fs.mkdirSync(constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed');
                }
                extractZip = att.inputFile;
                let basename = path.basename(extractZip);
                job.attrs.data.storeID = basename.split("-").reverse()[1];
                let storeName = job.attrs.data.storeID;
                let storeCode = storeName;
                let locationId = basename.split("-").reverse()[1];
                let mageGroupCode = basename.split("-")[0];
                let mageStoreCode =  basename.split("-")[1];

                uniqueFailLogName = locationId + '-' + mageStoreCode;

                console.log('Groupname:', mageGroupCode);
                segment.saveSegment(`Groupname : ${mageGroupCode}`);
                console.log('Location Id:', locationId);
                segment.saveSegment(`Location Id: : ${locationId}`);
                console.log('storeName:',mageStoreCode);
                segment.saveSegment(`storeName : ${mageStoreCode}`);

                let jobsTmp = await Agenda.jobs( {
                    $and: [
                        { "data.storeDataArray.locationId": locationId },
                        { "name": constants.REYNOLDS.JOB_NAME} ,
                        {"data.storeDataArray.mageStoreCode":mageStoreCode
                        }
                    ]
                });
                
                let projectId = '';
                let secondProjectId = '';
                let userName = '';
                let solve360Update = '';
                let updateSolve360Data; 
                let buildProxies;
                let extractionId;
                let invoiceMasterCSVFilePath;
                let inputStoreName;
                let etlDMSType;                  
                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;
                let switchBranch;
                let customBranchName;
                let mageManufacturer;
                let isPorscheStore;
                let haltIdentifier = false;
                let haltOverRide = false;
                let resumeUser;
                let projectIds;
                let secondProjectIdList;
                let uniqueId;
                let companyIds;
                let companyObj;
                let testData;
                let processorStatus;
                try{
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch(err){
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }
                
                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                segment.saveSegment(`Location Id : ${locationId}`);

                if(jobsTmp[jobsTmp.length-1]){
                    if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                        extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                        try{
                            segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                            agendaObject = agendaObject.filter(function (el) {
                                return el.locationId == locationId && el.mageStoreCode == mageStoreCode;
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if(agendaObject.length > 0){ 
                                agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }

                            if(extractedObjectIndex < 0){
                                extractedObjectIndex = 0;
                            }
                            
                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);        
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                projectId = agendaObject[extractedObjectIndex].projectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("invoiceMasterCSVFilePath")){
                               invoiceMasterCSVFilePath = agendaObject[extractedObjectIndex].invoiceMasterCSVFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("inputFilePath")){
                                inputStoreName = agendaObject[extractedObjectIndex].inputFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("etlDMSType")){
                                etlDMSType = agendaObject[extractedObjectIndex].etlDMSType;
                            } 
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("switchBranch")){
                                switchBranch = agendaObject[extractedObjectIndex].switchBranch;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("customBranchName")){
                                customBranchName = agendaObject[extractedObjectIndex].customBranchName;
                            } 

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                projectIds = agendaObject[extractedObjectIndex].projectIds;
                                projectIds =  projectIds.split("*");

                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                secondProjectIdList = secondProjectIdList.split("*");
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId+='-'+Date.now();
                           
                            }


                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                companyIds = agendaObject[extractedObjectIndex].companyIds;                               
                                companyIds = companyIds.replace(/\*/g, ',');
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                            }
                         if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                                testData = agendaObject[extractedObjectIndex].testData;
                            }
                            
                    
                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }

                updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:locationId, storeCode: mageStoreCode, dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'REYNOLDSRCI', groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj};
                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                console.log('projectId:', projectId);
                segment.saveSegment(`projectId : ${projectId}`);

                console.log('secondProjectId:', secondProjectId);
                segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log('solve360Update:', solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);
                
                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log('invoiceMasterCSVFilePath:', invoiceMasterCSVFilePath);
                segment.saveSegment(`invoiceMasterCSVFilePath : ${invoiceMasterCSVFilePath}`);

                console.log('inputStoreName:', inputStoreName);
                segment.saveSegment(`inputStoreName : ${inputStoreName}`);

                console.log('etlDMSType:', etlDMSType);
                segment.saveSegment(`etlDMSType : ${etlDMSType}`);


                console.log('switchBranch:', switchBranch);
                segment.saveSegment(`switchBranch : ${switchBranch}`);

                console.log('customBranchName:', customBranchName);
                segment.saveSegment(`customBranchName : ${customBranchName}`);

                console.log('haltOverRide:', haltOverRide);
                segment.saveSegment(`haltOverRide : ${haltOverRide}`);


                console.log('uniqueId:', uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log('companyIds:', companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);

                console.log('companyObj:', companyObj);
                segment.saveSegment(`companyObj : ${companyObj}`);

 		console.log('testData:', testData);
                segment.saveSegment(`testData : ${testData}`);
                  

                if(haltOverRide){
                    resumeUser = `${userName}`;
                    console.log('resumeUser:', resumeUser);
                    segment.saveSegment(`resumeUser : ${resumeUser}`);
                }

                if(mageManufacturer == constants.REYNOLDS.PORSCHE_STORE_LABEL){
                    isPorscheStore = true;
                } else{
                    isPorscheStore = false; 
                }

                console.log('mageManufacturer:', mageManufacturer);
                segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                console.log('isPorscheStore:', isPorscheStore);
                segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);


                let buildProxiesDecider;
                if(buildProxies){
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                } else{
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                }
                job.attrs.data.inputFilePath1 = inputStoreName;
                await job.save();

                if(haltOverRide){
                    // Portal update for process xml Resume
                    let todayDate;
                    let attPayload = {};    
                    let projectID;
                    let inpObjProject;
                    let inpObjSecondProject;
                    let projectIdList;
                    let secondProjectIdList;
                    try{
                    todayDate = new Date().toISOString().slice(0, 10);
                    attPayload = agendaObject[extractedObjectIndex];
                    projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                    projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                    secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                    attPayload['inProjectId'] =  projectID;
                    attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                    attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                    attPayload.in_retrive_ro_request_on = todayDate;
                    inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                    if(secondProjectIdList.length>0){
                        inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                    }
                    } catch(err){
                    console.log(JSON.stringify(err));
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                    }
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                    try {
                        segment.saveSegment(`AUTOMATE : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                         if(projectIdList){
                                companyObj.forEach(item => {
                                    if (item.projectId) {
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(item.projectId, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME,item.supplementTag || item.isPrimaryShipped);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`Delertrack Schedule portal call with Project Id RESUME${item.projectId}`);
                                        segment.saveSegment(`Dealertrack Schedule portal call with Project Id RESUME${item.projectId}`);
                
                                    }
                                 })
                            } 
                       
                        if(secondProjectIdList.length>0){
                        segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                        if(secondProjectIdList){
                            companyObj.forEach(item => {
                                if (item.secondProjectId != undefined && item.secondProjectId != '') {
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(item.secondProjectId, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME,item.secondarySupplementTag || item.isSecondaryShipped);
                                   portalUpdate.doPayloadAction(inpObjSecondProject);
                                   console.log(`Dealertrack Schedule portal call with Second Project Id Resume${item.secondProjectId}`);
                                   segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id RESUME${item.secondProjectId}`);           
                               }
                            });
                       } 
                        
                        }
                    } catch(error) {
                        console.log("Error:", error);
                        segment.saveSegment(`DEALERTRACK  : doPayloadAction Error - ${JSON.stringify(error)}`); 
                    }
                    //code end for portal update for process xml Resume
                }

                const processJson = spawn("bash",
                    [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                        buildProxiesDecider,
                        isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                        constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX,
                        constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.HALT_OVER_RIDE,
                        haltOverRide,
                        constants.PROCESS_JSON.INPUT_STORE_NAME,
                        inputStoreName,
                        constants.PROCESS_JSON.SINGLE_STORE_FLAG,
                        switchBranch,
                        constants.PROCESS_JSON.CUSTOM_BRANCH_NAME,
                        customBranchName,
                        constants.PROCESS_JSON.BRAND_NAME,mageManufacturer,
                        "--uuid",uniqueId,
                        "--performed-by",userName,
                        "--exception-report",true,
                        "--company_ids",companyIds
                    ], {
                        cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                        env: {
                            ...process.env,
                            PATH: `${process.env.PATH}:/usr/local/bin`
                          }                          
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                console.log(`Reynolds : Start processing of extractihaltOverRideon > ${basename}`);
                segment.saveSegment(`Reynolds : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`Reynolds : Start processing of extraction > ${basename}`, uniqueFailLogName);
                process.stdin.pipe(processJson.stdin);
                processJson.stdout.on("data", (data) => {
                    (async () => {                        
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                     
                    
                    data = data.toString('utf8');

                    if(data.includes('Processor status')){
                         segment.saveSegment('Processor statu for UI',data);
                         console.log('file generated',data.split(':')[1]);
                         processorStatus = data.split(':')[1];
                         segment.saveSegment('processorStatus',processorStatus);
                         await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                       }else{
                       console.log("failed to generate file")
                      }


                     await job.touch();
                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    })().catch((err) => {
                        console.error("Error in stdout data processing:", err);
                    });
                });

                processJson.stderr.on("data",  (data) => {
                    (async () => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);

                    try{
                        if(!data.includes('Beginning zip processing in') && data){
                            if (fs.existsSync(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip))) {
                                let deadLetterPath = `${process.env.DEALERTRACK_WORK_DIR}/dead-letter-processed`;
                                fs.copyFile(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip), deadLetterPath+ "/" + basename, (err) => {
                                    if (err) {
                                        console.log(err);
                                        segment.saveSegment(`Error in input file to dead letter: ${err}`);
                                        segment.saveSegmentFailure(`Error in input file to dead letter: ${err}`, storeCode);
                                    }
                                    console.log(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegment(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegmentFailure(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`, storeCode);
                                });                       

                            }
                        }
                    } catch(err){
                        console.log(err)
                    }
                    }
                    )().catch((err) => {
                        console.error("Error in stdout data processing:", err);
                    });                    
                });

                processJson.on("close", (code) => {
                   (async () => {
                    let message = "n/a";
                    let status = false;
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }
                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    let deadLetterPath = `${process.env.REYNOLDS_WORK_DIR}/dead-letter-processed`;
                    let errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp
                        status = false;
                    }
                    stdErrorArray.forEach((v,i) =>{
                        if(v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)){
                        }
                    })
                    job.attrs.data.processorUniqueId = '';
                    if(uniqueId && uniqueId!=undefined){
                        job.attrs.data.processorUniqueId = uniqueId;
                    }                   
                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////
                    let invalidmiscpaytypeFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv';
                    let  invalidmiscpaytypeCount;
                    let invalidmiscpaytypeArray;
                   
                    let estimateCount;
                    let estimateArray;
                    let estimateFilePath = "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv";
                    
                    let punchTimeMissingCount;
                    let punchTimeMissingArray;
                    let punchTimeMissingFilePath= "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt";
                    
                    let suffixedInvoicesCount;
                    let suffixedInvoicesArray;
                    let suffixedInvoicesFilePath =
                    "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv";
                    
                    let PUNCH_TIME_MISSING_FILEPATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv';

                    let exceptionClosedInvoicesFilePath ='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv';
                    let exceptionClosedInvoicesCount;
                    let exceptionClosedInvoicesArray;
                     
                    let extraRoInXmlExceptionFilePath ='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv';
                    let extraRoInXmlExceptionCount;
                    let extraRoInXmlExceptionArray;
                    
                    let imOpenedClosedRciRosFilePath= '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv';
                    let imOpenedClosedRciRosCount;
                    let imOpenedClosedRciRosArray;

                    let deletedRosFilepath='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv'
                    let deletedRosArray;
                    let deletedRoscount;

                    

                    let warningObj = {};
                    warningObj.scheduled_by = userName;
                    if(testData){
                        warningObj.testData = true;
                        warningObj.userName = userName;
                    }
                    if (fs.existsSync(invalidmiscpaytypeFilePath)) {
                                
                        console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`);
                        segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`);
                        segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`, storeCode);
                        
                        invalidmiscpaytypeArray = await csv().fromFile(invalidmiscpaytypeFilePath);

                        if(invalidmiscpaytypeArray){
                            if(invalidmiscpaytypeArray.length){

                            console.log(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`);
                            segment.saveSegment(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`);
                            segment.saveSegmentFailure(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`, storeCode);
                            
                            invalidmiscpaytypeCount = invalidmiscpaytypeArray.length;
                            console.log(invalidmiscpaytypeCount);
                            warningObj.invalidmiscpaytypeCount = invalidmiscpaytypeCount;
                            if(invalidmiscpaytypeCount) warningObj.invalidmiscpaytypeFilePath = invalidmiscpaytypeFilePath;
                        }
                    }

                        console.log(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegment(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegmentFailure(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`, storeCode);
                    }
                    if (fs.existsSync(estimateFilePath)) {
                        console.log(
                          `The estimate Csv File exists: ${estimateFilePath}`
                        );
                        segment.saveSegment(
                          `The estimate Csv File exists: ${estimateFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The estimate Csv File exists: ${estimateFilePath}`,
                          storeCode
                        );
            
                        estimateArray = await csv().fromFile(
                          estimateFilePath
                        );
            
                        if (estimateArray) {
                          if (estimateArray.length) {
                            console.log(
                              `estimateArray.length: ${estimateArray.length}`
                            );
                            segment.saveSegment(
                              `estimateArray.length: ${estimateArray.length}`
                            );
                            segment.saveSegmentFailure(
                              `estimateArray.length: ${estimateArray.length}`,
                              storeCode
                            );
            
                            estimateCount = estimateArray.length;
                            console.log('estimateCount',estimateCount);
                            warningObj.estimateCount = estimateCount;
                          }
                        }
            
                        console.log(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegment(
                          `invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`
                        );
                        segment.saveSegmentFailure(
                          `invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`,
                          storeCode
                        );
                      }           
                      if (fs.existsSync(punchTimeMissingFilePath)) {
                        console.log(
                          `The pubnh time missing Csv File exists: ${punchTimeMissingFilePath}`
                        );
                        segment.saveSegment(
                          `The Punch Time Missing Csv File exists: ${punchTimeMissingFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The Punch Time Missing Csv File exists: ${punchTimeMissingFilePath}`,
                          storeCode
                        );
            
                         fs.readFile(punchTimeMissingFilePath, 'utf8', (err, data) => {
                            if (err) {
                              console.error('Error reading the file:', err);
                              return;
                            }
                          
                            punchTimeMissingCount = data;
                            if(punchTimeMissingCount){

                                warningObj.punchTimeMissingCount = punchTimeMissingCount;
                                if (fs.existsSync(PUNCH_TIME_MISSING_FILEPATH)) {
                                    // The file or directory at the specified path exists
                                    console.log('PUNCH_TIME_MISSING csv file Exists');
                                    warningObj.PUNCH_TIME_MISSING_FILEPATH = PUNCH_TIME_MISSING_FILEPATH;
                                  } else {
                                    // The file or directory does not exist
                                    console.log('PUNCH_TIME_MISSING csv file Does not exist');
                                  }

                            }
                          });
                          

                         console.log(`punchTimeMissingCount: ${punchTimeMissingCount}`);
                         segment.saveSegment(
                          `punchTimeMissingCount: ${punchTimeMissingCount}`
                         );
                        segment.saveSegmentFailure(
                          `punchTimeMissingCount: ${punchTimeMissingCount}`,
                          storeCode
                        );
                      }

                      if (fs.existsSync(suffixedInvoicesFilePath)) {
                        console.log(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`
                        );
                        segment.saveSegment(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`,
                          storeCode
                        );
            
                        suffixedInvoicesArray = await csv().fromFile(
                          suffixedInvoicesFilePath
                        );
            
                        if (suffixedInvoicesArray) {
                          if (suffixedInvoicesArray.length) {
                            console.log(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`
                            );
                            segment.saveSegment(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`
                            );
                            segment.saveSegmentFailure(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`,
                              storeCode
                            );
            
                            suffixedInvoicesCount = suffixedInvoicesArray.length;
                            console.log('suffixedInvoicesCount',suffixedInvoicesCount);
                            warningObj.suffixedInvoicesCount = suffixedInvoicesCount;
                            if (suffixedInvoicesCount){
                                warningObj.suffixedInvoicesFilePath =
                                suffixedInvoicesFilePath;

                            }                              
                          }
                        }
                      
                        console.log(`suffixedInvoicesCount: ${suffixedInvoicesCount}`);
                        segment.saveSegment(
                          `suffixedInvoicesCount: ${suffixedInvoicesCount}`
                        );
                        segment.saveSegmentFailure(
                          `suffixed invoice count: ${suffixedInvoicesCount}`,
                          storeCode
                        );

                      }
                      
                    let groupedSuffixInvoiceFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/grouped_suffixinvoice.csv';

                    let suffixedInvoicesCsvData='';
                    if (fs.existsSync(groupedSuffixInvoiceFilePath)) {
                          fs.createReadStream(groupedSuffixInvoiceFilePath)
                          .pipe(csvParser())
                          .on('data', (row) => {
                          suffixedInvoicesCsvData+=`${row.source}:${row.date}:${row.ROCount}*`
                          })
                          .on('end', () => {
                            // CSV parsing is complete
                            console.log('CSV parsing finished.');
                            console.log("suffixedInvoicesCsvData",suffixedInvoicesCsvData);
                          })
                          .on('error', (error) => {
                            // Handle any error that occurs during parsing
                            console.error('Error occurred while parsing CSV:', error);
                          });
                    }


             
                    if (fs.existsSync(exceptionClosedInvoicesFilePath)) {
                                
                        console.log(`The Exception Closed Invoices File exists: ${exceptionClosedInvoicesFilePath}`);
                        segment.saveSegment(`The Exception Closed Invoices File exists: ${exceptionClosedInvoicesFilePath}`);
                        segment.saveSegmentFailure(`The Exception Closed Invoices File  exists: ${exceptionClosedInvoicesFilePath}`, storeCode);
                        
                        exceptionClosedInvoicesArray = await csv().fromFile(exceptionClosedInvoicesFilePath);

                        if(exceptionClosedInvoicesArray){
                            if(exceptionClosedInvoicesArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`, storeCode);
                            
                            exceptionClosedInvoicesCount = exceptionClosedInvoicesArray.length;
                            console.log(exceptionClosedInvoicesCount);
                            warningObj.exceptionClosedInvoicesCount = exceptionClosedInvoicesCount;
                            if(exceptionClosedInvoicesCount) warningObj.exceptionClosedInvoicesFilePath = exceptionClosedInvoicesFilePath;
                            
                        }
                    }

                        console.log(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`);
                        segment.saveSegment(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`);
                        segment.saveSegmentFailure(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`, storeCode);
                    }

                



                    if (fs.existsSync(extraRoInXmlExceptionFilePath)) {
                                
                        console.log(`The Exception Closed Invoices File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegment(`The Exception Closed Invoices File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegmentFailure(`The Exception Closed Invoices File  exists: ${extraRoInXmlExceptionFilePath}`, storeCode);
                        
                        exceptionClosedInvoicesArray = await csv().fromFile(extraRoInXmlExceptionFilePath);

                        if(extraRoInXmlExceptionArray){
                            if(extraRoInXmlExceptionArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`, storeCode);
                            
                            extraRoInXmlExceptionCount = extraRoInXmlExceptionArray.length;
                            console.log(extraRoInXmlExceptionCount);
                            warningObj.extraRoInXmlExceptionCount = extraRoInXmlExceptionCount;
                            if(extraRoInXmlExceptionCount) warningObj.extraRoInXmlExceptionFilePath = extraRoInXmlExceptionFilePath;
                            
                        }
                    }

                        console.log(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegment(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegmentFailure(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`, storeCode);
                    }




                    if (fs.existsSync(extraRoInXmlExceptionFilePath)) {
                                
                        console.log(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegment(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegmentFailure(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`, storeCode);
                        
                        extraRoInXmlExceptionArray = await csv().fromFile(extraRoInXmlExceptionFilePath);

                        if(extraRoInXmlExceptionArray){
                            if(extraRoInXmlExceptionArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`, storeCode);
                            
                            extraRoInXmlExceptionCount = extraRoInXmlExceptionArray.length;
                            console.log(extraRoInXmlExceptionCount);
                            warningObj.extraRoInXmlExceptionCount = extraRoInXmlExceptionCount;
                            if(extraRoInXmlExceptionCount) warningObj.extraRoInXmlExceptionFilePath = extraRoInXmlExceptionFilePath;
                            
                        }
                    }

                        console.log(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegment(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegmentFailure(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`, storeCode);
                    }

                    
                      
            
                    
                    if (fs.existsSync(imOpenedClosedRciRosFilePath)) {
                                
                        console.log(`The imOpendedClosedRciRos exists: ${imOpenedClosedRciRosFilePath}`);
                        segment.saveSegment(`The imOpendedClosedRciRos File exists: ${imOpenedClosedRciRosFilePath}`);
                        segment.saveSegmentFailure(`The imOpendedClosedRciRosFile  exists: ${imOpenedClosedRciRosFilePath}`, storeCode);
                        
                        imOpenedClosedRciRosArray = await csv().fromFile(imOpenedClosedRciRosFilePath);

                        if(imOpenedClosedRciRosArray){
                            if(imOpenedClosedRciRosArray.length){

                            console.log(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`);
                            segment.saveSegment(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`);
                            segment.saveSegmentFailure(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`, storeCode);
                            
                            imOpenedClosedRciRosCount = imOpenedClosedRciRosArray.length;
                            console.log(imOpenedClosedRciRosCount);
                            warningObj.imOpenedClosedRciRosCount = imOpenedClosedRciRosCount;
                            if(imOpenedClosedRciRosCount) warningObj.imOpenedClosedRciRosFilePath = imOpenedClosedRciRosFilePath;
                            
                        }
                    }

                        console.log(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`);
                        segment.saveSegment(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`);
                        segment.saveSegmentFailure(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`, storeCode);
                    }


                     
                    if (fs.existsSync(deletedRosFilepath)) {
                                
                        console.log(`The deletedRosFilepath exists: ${deletedRosFilepath}`);
                        segment.saveSegment(`The deletedRosFilepath File exists: ${deletedRosFilepath}`);
                        segment.saveSegmentFailure(`The deletedRosFilepath  exists: ${deletedRosFilepath}`, storeCode);
                        
                        deletedRosArray = await csv().fromFile(deletedRosFilepath);

                        if(deletedRosArray){
                            if(deletedRosArray.length){

                            console.log(`deletedRosArray.length: ${deletedRosArray.length}`);
                            segment.saveSegment(`deletedRosArray.length: ${deletedRosArray.length}`);
                            segment.saveSegmentFailure(`deletedRosArray.length: ${deletedRosArray.length}`, storeCode);
                            
                            deletedRoscount = deletedRosArray.length;
                            console.log(deletedRoscount);
                            warningObj.deletedRoscount = deletedRoscount;
                            if(deletedRoscount) warningObj.deletedRosFilepath = deletedRosFilepath;
                            
                        }
                    }

                        console.log(`deletedRoscount: ${deletedRoscount}`);
                        segment.saveSegment(`deletedRoscount: ${deletedRoscount}`);
                        segment.saveSegmentFailure(`deletedRoscount: ${deletedRoscount}`, storeCode);
                    }
                      
                      

                    console.log(stdErrorArray);
                    if (stdErrorArray && !haltOverRide) {

                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                        for (const v of stdErrorArray) {
                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                            ) {
                                message = "Halt";
                                haltIdentifier = true;
                                status = false;
                                await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,message);
                            }

                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                            ) {
                                message = "Dead";
                                await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,message);
                                haltIdentifier = true;
                                status = false;
                            }


                        }

                        try {
                            if (
                                stdOutArray &&
                                stdOutArray.includes(errResp) &&
                                message == "Halt" &&
                                !haltOverRide
                            ) {
                                let deadLetterFilePath = deadLetterPath + "/" + basename;
                                let haltFilePath =
                                    "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/halt/" +
                                    basename;
                                if (fs.existsSync(deadLetterFilePath)) {
                                    fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                        if (err)
                                        {
                                            console.log('err',err);
                                        }
                                        else{
                                        console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`)
                                        }
                                    });
                                } else {
                                    console.log(`${deadLetterFilePath} not exist!`);
                                }
                            } else{
                                console.log('Not a Halt process')
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////
                    let fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                    let groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                    let storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';

                    console.log(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegment(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegmentFailure(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                    let extractionErrorResponse;
                    let errorWarnningMessage;
                    

                    if(extractionId){
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'Reynolds');
                        console.log('extractionErrorResponse:',extractionErrorResponse);
                        if(extractionErrorResponse.status){
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                tmpDescritionArray.push(e.description);
                                });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    } 
                    console.log('errorWarnningMessage:',errorWarnningMessage);

                    if(errorWarnningMessage){
                        if(errorWarnningMessage.length > 0){
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }

                    let failureDirectory = process.cwd() + '/logs/Reynolds/failure/';
                    let failurelogFile = failureDirectory + uniqueFailLogName + '.log';


                    console.log(warningObj);
                    let mailTemplateReplacementValues = {
                        dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'REYNOLDSRCI',
                        processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'REYNOLDSRCI-PROCESS-JSON',
                        subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                        warningObj: warningObj,
                        thirdPartyUsername: locationId,
                        storeCode: storeName,
                        groupCode: groupName
                    };
                    let mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile:failurelogFile
                    }

                       job.attrs.data.processorUniqueId = '';
                     
                       if(uniqueId && uniqueId!=undefined){
                         job.attrs.data.processorUniqueId = uniqueId;
                        }
                   
                   
                    if (status) {
                        clearInterval(touch);
                        let opDataFileEtl = path.join(constants.PROCESS_JSON.REYNOLDS_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        let opDataFileDist = path.join(constants.PROCESS_JSON.REYNOLDS_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        let outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        job.attrs.data.warningMessage = warningObj;
                        job.attrs.data.invalidmiscpaytypeCount =  invalidmiscpaytypeCount;
                        job.attrs.data.estimateCount = estimateCount;
                        job.attrs.data.punchTimeMissingCount = punchTimeMissingCount;
                        job.attrs.data.suffixedInvoicesCount = suffixedInvoicesCount;
                        job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                        await job.save();
                        segment.saveSegment(`Job Closed for job:${basename} status:${status}`);
                        done();
                      
                        // Send notification after process json job completed
                        let basenameCheck1 = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        let zipPath = path.join(constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR, basenameCheck1);
                        let outPutTemp = constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR+'/temp1';
                        let invoice_missing_count='';
                            
                            fs.createReadStream(zipPath)
                            .pipe(unZipper .Extract({ path: outPutTemp }))
                            .on ('close',async (res)=>{
                            if(fs.existsSync(`${outPutTemp}/processing-result/missing-invoices.csv`)){
                                
                            let  missing_ro_countArray = await csv().fromFile(`${outPutTemp}/processing-result/missing-invoices.csv`);
                            invoice_missing_count = missing_ro_countArray.length;
                                
                    }else{
                             console.log('false');
                         }

                            })
                       
                            await segment.sleep(8000); 

                            warningObj.invoice_missing_count = invoice_missing_count;
                            warningObj.missingInvoiceFilePath = `${outPutTemp}/processing-result/missing-invoices.csv`;


                            let mailTemplateReplacementValues = {
                                dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'REYNOLDSRCI',
                                processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'REYNOLDSRCI-PROCESS-JSON',
                                subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                                warningObj: warningObj,
                                thirdPartyUsername: locationId,
                                storeCode: storeName,
                                groupCode: groupName
                            };

                            let displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);                        
                            await segment.sleep(15000); 
                                  if(fs.existsSync(outPutTemp)){                        
                                removeDirForce(outPutTemp);                            
                                function removeDirForce(dirPath) {
                                    let files;
                                    try {  files = fs.readdirSync(dirPath); }
                                    catch (e) { 
                                        console.error(`Error reading directory: ${e.message}`);
                                        return;
                                    }

                                    if (files.length > 0)
                                        for (const file of files) {
                                            let filePath = dirPath + '/' + file;
                                            if (fs.statSync(filePath).isFile()) {
                                                fs.unlinkSync(filePath);
                                            } else {
                                                removeDirForce(filePath);
                                            }
                                        }                                       
                                    fs.rmdirSync(dirPath);                               
                                }
                          }
                             else{
                                console.log("file not exit remove");
                             }

                    } else {
                        clearInterval(touch);
                        // Portal update for process json failed

                        const directoryPath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/'; 
                        const fileName = basename;                                 
                        const filePath = path.join(directoryPath, fileName);
                        segment.saveSegment(`Autosoft : filePath inpObj Error - ${fileName}`);
                         
                        if (fs.existsSync(filePath)) {
                             fs.unlink(filePath, (err) => {
                               if (err) {
                                segment.saveSegment(`Autosoft : Error deleting file - ${err}`);
                                console.error('Error deleting file:', err);
                               } else {
                                segment.saveSegment(`Autosoft : File deleted successfully - ${filePath}`);
                                console.log('File deleted successfully:', filePath);
                                }
                            });
                        } else {
                           console.log('File does not exist:', filePath);
                     }
                        

                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let projectIdList;
                        let secondProjectIdList;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                        secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                        attPayload['inProjectId'] =  projectID;

                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        job.attrs.data.invalidmiscpaytypeCount =  invalidmiscpaytypeCount;
                        job.attrs.data.estimateCount = estimateCount;
                        job.attrs.data.punchTimeMissingCount = punchTimeMissingCount;
                        job.attrs.data.suffixedInvoicesCount = suffixedInvoicesCount;
                        job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate,  haltIdentifier ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                        if(secondProjectIdList.length > 0){
                            inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, haltIdentifier ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`Reynolds : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`Reynolds : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`Reynolds : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`Reynolds : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            if(projectIdList){
                                 companyObj.forEach(item => {
                                    if (item.projectId) {
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(item.projectId, attPayload, todayDate,  haltIdentifier ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL,item.supplementTag || item.isPrimaryShipped);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`Reynolds Schedule portal call with Project Id Failure${item.projectId}`);
                                        segment.saveSegment(`REYNOLDS Schedule portal call with Project Id FAILURE${item.projectId}`);
                
                                    }
                                 });
                            } 
                            if(secondProjectId){
                            if(secondProjectIdList){
                            companyObj.forEach(item => {
                                if (item.secondProjectId != undefined && item.secondProjectId != '') {
                                      inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(item.secondProjectId, attPayload, todayDate,  haltIdentifier ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL, item.secondarySupplementTag || item.isSecondaryShipped);
                                       portalUpdate.doPayloadAction(inpObjSecondProject);
                                       console.log(`Reynolds Schedule portal call with Second Project Id Failure${item.secondProjectId}`);
                                       segment.saveSegment(`REYNOLDS Schedule portal call with Second Project Id FAILURE${item.secondProjectId}`);
               
                                   }
                                });
                           } 
                        }
                           
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`Reynolds : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process json failed


                        await job.fail(new Error(`Error: ${message}`));
                        segment.saveSegment(`Job Closed for job:${basename} status:${status}`);
                        done();
                        let displayMessage =''
                        if(haltIdentifier){
                            if(message == 'Halt'){
                                displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Halted';
                                mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Halted`;
                            }
                        } else{
                               if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser || '';
                               } 
                            
                        displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Failed';
                        mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                            }
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                        // Send notification for failed process json job
                        await segment.sleep(2000);
                        // Send notification for failed process json job
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                    }
                    console.log(`Call for next job selection`);
                    segment.saveSegment(`Call method for SharePoint data upload`);
                    segment.saveSegment(`Call for next job selection`);
                    let basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    let distFile = path.join(constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR, basenameCheck)

                    let exceptions = "";


                      if(punchTimeMissingCount){
                        if (punchTimeMissingCount !== undefined && punchTimeMissingCount >40) {
                            exceptions += `Punch Time Missing percentage: ${punchTimeMissingCount}, `;
                          }
                      }
                   
                      if(suffixedInvoicesCount){
                        if (suffixedInvoicesCount !== undefined && suffixedInvoicesCount >40) {
                            exceptions += `Suffixed Invoices Count: ${suffixedInvoicesCount}, `;
                          }
      
                      }

                      if(extraRoInXmlExceptionCount){
                        if (extraRoInXmlExceptionCount !== undefined){
                            exceptions += `Extra ROs in Raw Data: ${extraRoInXmlExceptionCount}, `;
                          }
      
                      }

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:locationId, storeCode: mageStoreCode, dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'REYNOLDSRCI', groupCode:mageGroupCode,exceptions:exceptions,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,companyObj:companyObj};
                    
                    if (status && fs.existsSync(distFile)) {
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        await distributeFile(basename, null, updateSolve360Data, warningObj, etlDMSType, job.attrs._id);

                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment(`Call for next job selection`);
                        await doNextProcess();
                    }
                })().catch(err => {
                console.error("Error in data handler:", err);
                });
                });
            } else {                
                /**
                * Remove the Initial/recheck schedules
                */
                if(job.attrs.data.operation=="recheck"){
                job.remove(err => {
                    if (!err) {
                        console.log("Initial/recheck schedule for Reynolds Process JSON job successfully removed");
                    }
                });
              }
                done();
                await doNextProcess();
            }
        });

    return agenda;
}
