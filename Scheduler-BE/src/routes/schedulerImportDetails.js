const express = require('express');
const routes = express.Router();
const { logger } = require("../logger/schedulerLogger");
const rp = require('request-promise');
const appConstants = require('../common/constants');
const segment = require("../controllers/SEGMENT/segmentManager");
const request = require('request');
const { ConsoleReporter } = require('jasmine');
const path = require('path');

var inActiveBundle;
var inSecondaryActiveBundle;

function buildQuery(inSubmissionId) {
    return `query {
        getSchedulerImportDetailsBySubmissionUuid(inSubmissionUuid: "${inSubmissionId}")
    }`;
}

function buildSchedulerImportQuery(inSubmissionId, isInputFileGenerated, isMapperLoad, isAuditLoad, isInputFileGeneratedComment,
    isMapperLoadComment,
    isAuditLoadComment
) {
    return `mutation {
        updateSchedulerImportDetails(input: {
            inSubmissionUuid: "${inSubmissionId}",
            inIsInputFileGenerated: ${isInputFileGenerated},
            inIsMapperLoad: ${isMapperLoad},
            inIsAuditLoad: ${isAuditLoad},
            inIsInputFileGeneratedComment: ${isInputFileGeneratedComment ? `"${isInputFileGeneratedComment}"` : null},
            inIsMapperLoadComment: ${isMapperLoadComment ? `"${isMapperLoadComment}"` : null},
            inIsAuditLoadComment: ${isAuditLoadComment ? `"${isAuditLoadComment}"` : null}
        }) {
            json
        }
    }`;
}

function getOptions(URI, query) {
    return {
        method: 'POST',
        uri: URI,
        body: JSON.stringify({ query: query }),
        headers: {
            'Content-Type': 'application/json',
        },
    };
}
function buildSchedulerDetailsQuery(inSchedulerId) {
    return `query {
      getScheduleDetailBySchedulerId(inSchedulerId: "${inSchedulerId}")
    }`;
  }

  function getCompanyDetails(schedulerId) {
    console.log("Inside get company details?????????????????????????????????????????????????",schedulerId);
    return new Promise((resolve, reject) => {
        let scheduleDetailsQuery = buildSchedulerDetailsQuery(schedulerId);
        const schedulerDetailsOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, scheduleDetailsQuery);

        rp(schedulerDetailsOptions)
            .then(async (response) => {
                // First parse the response string into an object
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(response);
                } catch (parseError) {
                    console.error("Failed to parse response JSON:", parseError);
                    logger.error("Failed to parse response JSON:", parseError);
                    return reject(new Error("Response JSON is invalid"));
                }

                console.log(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);
                logger.info(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);

                const rawDetails = parsedResponse?.data?.getScheduleDetailBySchedulerId;

                if (typeof rawDetails === 'string') {
                    let scheduleDetails;
                    try {
                        scheduleDetails = JSON.parse(rawDetails);
                    } catch (innerParseError) {
                        console.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        logger.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        return reject(new Error("getScheduleDetailBySchedulerId JSON is invalid"));
                    }

                    const activeBundles = scheduleDetails.map(item => item.is_active_bundle);
                    isActiveBundle = activeBundles;
                    resolve(activeBundles);
                } else {
                    reject(new Error('Invalid response structure or getScheduleDetailBySchedulerId is not a string'));
                }
            })
            .catch((error) => {
                console.error("Error in getCompanyDetails:", error);
                logger.error("Error in getCompanyDetails:", error);
                reject(error);
            });
    });
}




function buildSchedulerDetailsQuery(inSchedulerId) {
    return `query {
      getScheduleDetailBySchedulerId(inSchedulerId: "${inSchedulerId}")
    }`;
  }

function buildSchedulerDetailsQuery1(inSchedulerId, inCompanyId) {
  return `query {
    getScheduleDetailBySchedulerIdAndCompanyId(
      inSchedulerId: "${inSchedulerId}", 
      inCompanyId: "${inCompanyId}"
    )
  }`;
}



  function updateAudit(schedulerId,inSubmissionId,companyId){

       console.log("Update Audit%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",schedulerId);
        
       console.log("inSubmissionId%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",inSubmissionId);
        console.log("companyId%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",companyId);

       let fileName = "";
       let createdBy ="";

       return new Promise((resolve, reject) => {
        let scheduleDetailsQuery = buildSchedulerDetailsQuery1(schedulerId,companyId);
        const schedulerDetailsOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, scheduleDetailsQuery);

        rp(schedulerDetailsOptions)
            .then(async (response) => {
                console.log("response - buildSchedulerDetailsQuery1$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",response);
                // First parse the response string into an object
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(response);
                    console.log("parsedResponse$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",parsedResponse)
                } catch (parseError) {
                    console.error("Failed to parse response JSON:", parseError);
                    logger.error("Failed to parse response JSON:", parseError);
                    return reject(new Error("Response JSON is invalid"));
                }

                console.log(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);
                logger.info(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);
                const rawDetails = parsedResponse?.data?.getScheduleDetailBySchedulerIdAndCompanyId;

                console.log("rawDetails@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",rawDetails);

                if (typeof rawDetails === 'string') {
                    let scheduleDetails;
                    try {
                        scheduleDetails = JSON.parse(rawDetails);
                    } catch (innerParseError) {
                        console.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        logger.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        return reject(new Error("getScheduleDetailBySchedulerId JSON is invalid"));
                    }

                    console.log("Scheduler deatils$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",scheduleDetails)

                     fileName = scheduleDetails.map(item => item.bundle_file_name);
                     createdBy = scheduleDetails.map(item => item.created_by);

                     console.log("fileName@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",fileName);
                      console.log("createdBy@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",createdBy);



                             const mutation = `
                                mutation SaveProxyRoImport(
                                  $inSubmissionUuid: String!,
                                  $inUrl: String!,
                                  $inMetaData: JSON!,
                                  $inCreatedBy: String!
                                  ) {
                                     saveProxyRoImport(input: {
                                     inSubmissionUuid: $inSubmissionUuid,
                                     inUrl: $inUrl,
                                     inMetaData: $inMetaData,
                                     inCreatedBy: $inCreatedBy
                                    }) {
                                    json
                                  }
                              }
                            `;



const variables = {
  inSubmissionUuid: inSubmissionId,   
  inUrl: path.parse(fileName[0]).name,                         
  inMetaData: JSON.stringify({}),   
  inCreatedBy: createdBy[0]                 
};

console.log("Scheduler import variables$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",variables);

const options = {
  method: 'POST',
  uri: appConstants.conf.GRAPHQL_AUDIT_URI,
  body: {
    query: mutation,
    variables: variables,
  },
  json: true,
};

console.log('Sending mutation with variables:', options);

rp(options)
  .then((response) => {
    console.log('GraphQL AUDIT Response::::::::::::::::::::::::::::::::::::::::::::::::::::::::::', response);
    segment.saveSegment(`SaveProxyRoImport Success: ${JSON.stringify(response)}`);
  })
  .catch((err) => {
    console.error('GraphQL AUDIT Error:::::::::::::::::::::::::::::::::::::::::::::::::::::::::', err.message);
    segment.saveSegment(`SaveProxyRoImport Error: ${err.message}`);
  });
                    // resolve(activeBundles);
                } else {
                    reject(new Error('Invalid response structure or getScheduleDetailBySchedulerId is not a string'));
                }
            })
            .catch((error) => {
                console.error("Error in getCompanyDetails:", error);
                logger.error("Error in getCompanyDetails:", error);
                reject(error);
            });
    });
     
  }

  let isActiveBundle;
  function getCompanyDetails(schedulerId) {
    console.log("Inside get company details?????????????????????????????????????????????????",schedulerId);
    return new Promise((resolve, reject) => {
        let scheduleDetailsQuery = buildSchedulerDetailsQuery(schedulerId);
        const schedulerDetailsOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, scheduleDetailsQuery);

        rp(schedulerDetailsOptions)
            .then(async (response) => {
                // First parse the response string into an object
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(response);
                } catch (parseError) {
                    console.error("Failed to parse response JSON:", parseError);
                    logger.error("Failed to parse response JSON:", parseError);
                    return reject(new Error("Response JSON is invalid"));
                }

                console.log(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);
                logger.info(`getCompanyDetails Response: ${JSON.stringify(parsedResponse)}`);

                const rawDetails = parsedResponse?.data?.getScheduleDetailBySchedulerId;

                if (typeof rawDetails === 'string') {
                    let scheduleDetails;
                    try {
                        scheduleDetails = JSON.parse(rawDetails);
                    } catch (innerParseError) {
                        console.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        logger.error("Failed to parse getScheduleDetailBySchedulerId JSON:", innerParseError);
                        return reject(new Error("getScheduleDetailBySchedulerId JSON is invalid"));
                    }

                    const activeBundles = scheduleDetails.map(item => item.is_active_bundle);
                    isActiveBundle = activeBundles;
                    resolve(activeBundles);
                } else {
                    reject(new Error('Invalid response structure or getScheduleDetailBySchedulerId is not a string'));
                }
            })
            .catch((error) => {
                console.error("Error in getCompanyDetails:", error);
                logger.error("Error in getCompanyDetails:", error);
                reject(error);
            });
    });
}
async function getStatusFromSchedulerDB(isInputFileGenerated, isMapperLoad, isAuditLoad, inSubmissionId, inPerformedBy, inProjectId, inSchedulerId, inSource, inAction, inDms, inCreatedBy, inPerformedOn, inSecondaryProjectId,inProcessKey) {
    let responseArray = [];
    let apiPromises = [];
    let statusResponse;
    var resumedBy; 

  

    return new Promise((resolve, reject) => {

        if (isInputFileGenerated || isMapperLoad || isAuditLoad) {
            //For fetch get scheduler import query
            const schedulerQuery = buildQuery(inSubmissionId);
            const schedulerOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerQuery);
            apiPromises.push(rp(schedulerOptions)
                .then(response => {
                  
                    if (!response) {
                        statusResponse = {
                            status: "Failed",
                            message: "No response from GraphQL query",
                            data: "",
                            apiError: ""
                        };
                        responseArray.push(statusResponse);
                        logger.info(`STEP1 importPortalCheck No response from GraphQL query : ${JSON.stringify(response)}`);
                       

                        // resolve({ status: "failed", message: "No response from GraphQL query", data: "", apiError: '' })
                        // return res.status(200).json({ status: "failed", message: "No response from GraphQL query" });
                    } else {
                        logger.info(`STEP1 importPortalCheck GET_SCHEDULER_IMPORT_QUERY Successfully fetched query : ${JSON.stringify(response)}`);
                        try {
                            const responseData = JSON.parse(response);
                            const data = JSON.parse(responseData.data.getSchedulerImportDetailsBySubmissionUuid); //nulll
                         
                       ;
                
                             resumedBy = data[0].resumed_by;
                            
                            logger.info(`************************STEP2 importPortalCheck GET_SCHEDULER_IMPORT_QUERY DATA: ${data}`);
                            logger.info(`Resumed by from database::${resumedBy}`)
                            logger.info(`userName  from processor::${inPerformedBy}`)
                            console.log("dat#################################################################",data);

                            // Check if is_input_file_generated, is_mapper_load, and is_audit_load are true
                            if (!data) {
                                logger.info(`STEP3 importPortalCheck GET_SCHEDULER_IMPORT_QUERY No response data`);
                                // res.status(200).json({ status: "success", data: "No response data!!!" });
                                statusResponse = {
                                    status: "success",
                                    message: "No response data!!! SubmissionUuid missing!!!",
                                    data: '',
                                    apiError: ''
                                };
                                responseArray.push(statusResponse);
                                logger.info(`STEP3.1 importPortalCheck GET_SCHEDULER_IMPORT_QUERY responseArray`,JSON.stringify(responseArray));

                                // resolve({ status: "success", message: "No response data!!!", data: "", apiError: '' })
                            } else {
                                const statusObj = data.every(item =>
                                    item.is_mapper_load === true &&
                                    item.is_audit_load === true &&
                                    item.is_input_file_generated === true
                                );

                                const auditStatus =  data.every(item =>
                                    
                                    item.is_audit_load === true 
                                );
                                let company_id = data[0].company_id;


                                console.log("auditStatus$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",data);
                                 console.log("company_id$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",company_id);

                                if(auditStatus){
                                       console.log("inside audit status%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
                                     updateAudit(inSchedulerId,inSubmissionId,company_id);
                                }

                                logger.info("*************************************************")
                                logger.info("succesUpdateStatusData STEP3 importPortalCheck", inPerformedBy, data, data.is_mapper_load, data.is_audit_load, data.is_import_completed, statusObj)
                                logger.info("*************************************************")

                                if (statusObj) {

                                    logger.info("*************************************************")
                                    logger.info("successtatusObj STEP4 importPortalCheck", statusObj)
                                    logger.info("*************************************************")
                                        // Check if is_input_file_generated, is_mapper_load, and is_audit_load are true
                                        // let payload;
                                    let payload = {};


                                    if (isAuditLoad) {
                                        console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$INSIDE AUDIT LOAD$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");

                                      

                                        payloadP = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inSchedulerId: inSchedulerId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inDms: inDms,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy,
											inProcessKey:inProcessKey
                                        };
                                        payloadS = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inSchedulerId: inSchedulerId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inDms: inDms,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy,
											inProcessKey:inProcessKey
                                        };
                                        logger.info("*************************************************")
                                        logger.info("isAuditLoad STEP5 importPortalCheck", payloadP, payloadS)
                                        logger.info("*************************************************")
                                    } else if (isMapperLoad) {
                                        console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$INSIDE MAPPER LOAD$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");

                                        payloadP = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                                        };
                                        payloadS = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                                        };
                                        logger.info("*************************************************")
                                        logger.info("isMapperLoad STEP6 importPortalCheck", payload)
                                        logger.info("*************************************************")
                                    } else if (isInputFileGenerated) {
                                        payloadP = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inSchedulerId: inSchedulerId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inDms: inDms,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                                        };
                                        payloadS = {
                                            inData: {},
                                            inProjectId: '',
                                            inSource: "SchedulerImport",
                                            inAction: "complete",
                                            inSubmissionId: inSubmissionId,
                                            inSchedulerId: inSchedulerId,
                                            inPerformedOn: inPerformedOn,
                                            inAssignee: resumedBy ? resumedBy : inPerformedBy,
                                            inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                                            inDms: inDms,
                                            inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                                        };
                                        logger.info("*************************************************")
                                        logger.info("isInputFileGenerated STEP6 importPortalCheck", payload)
                                        logger.info("*************************************************")
                                    }

                                    if (inProjectId) {
                                        payloadP.inProjectId = inProjectId; // Set primary projectID
                                        payloadP.inActiveBundle = inActiveBundle;

                                      console.log("PAYLOAD FOR COMPLETE CALL**********************",payloadP);

                                        const apiOptionsP = {
                                            method: 'POST',
                                            uri: appConstants.conf.PAYLOAD_URI,
                                            body: payloadP,
                                            json: true,
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                        };

                                        console.log("INSIDE COMPLETE&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",apiOptionsP)

                                        logger.info("*************************************************")
                                        logger.info("apiOptionsProjectid STEP7 importPortalCheck", apiOptionsP)
                                        logger.info("*************************************************")
                                            //for portal API URL
                                        apiPromises.push(rp(apiOptionsP)
                                            .then(apiResponse => {
                                                logger.info(`POST_PORTAL_API Response from API projrct id endpoint STEP8 importPortalCheck: ${JSON.stringify(apiResponse)}`);
                                                console.log("API RESPONSE FROM COMPLETE CALL%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",JSON.stringify(apiResponse));
                                                logger.info(`POST_PORTAL_API Response from API endpoint STEP9 importPortalCheck: ${JSON.stringify(apiResponse)}`)
                                                logger.info("PortalapiResponse STEP10 importPortalCheck----------------------------------")

                                                logger.info(apiResponse, "SuccessPortalapiResponse STEP11 importPortalCheck")
                                                logger.info("PortalapiResponse----------------------------------")
                                                statusResponse = {
                                                    status: apiResponse.status,
                                                    message: apiResponse.message,
                                                    data: payloadP,
                                                    apiError: ''
                                                };
                                                responseArray.push(statusResponse);
                                                logger.info("responseArraystatusObjData STEP12 importPortalCheck", JSON.stringify(responseArray))

                                                //resolve({ status: apiResponse.status, message: apiResponse.message, data: payload, apiError: '' });
                                                // res.status(200).json({ status: "success", data: payload });
                                            })
                                            .catch(apiError => {
                                                logger.info("PortalapiResponse----------------------------------")
                                                logger.info(apiResponse, "STEP12 importPortalCheck FailPortalapiResponse project id case ")
                                                logger.info("PortalapiResponse----------------------------------")
                                                logger.error(`POST_PORTAL_API Error from fail API endpoint STEP12.1 importPortalCheck: ${JSON.stringify(apiError)}`);
                                                // res.status(200).json({ status: "error", apiError: apiError.message });
                                                statusResponse = {
                                                    status: "Failed",
                                                    message: "Fail portal api response in project id case",
                                                    data: payloadP,
                                                    apiError: ''
                                                };
                                                responseArray.push(statusResponse);
                                                logger.info("responseArraystatusFailObjData STEP12.0 importPortalCheck", JSON.stringify(responseArray))

                                                // resolve({ status: "error", message: "success", data: payload, apiError: apiError.message });

                                            }));

                                    }
                                    if (inSecondaryProjectId) {
                                        payloadS.inProjectId = inSecondaryProjectId; // Set secondary projectID
                                        payloadS.inActiveBundle = inSecondaryActiveBundle;
                                        const apiOptionsSec = {
                                            method: 'POST',
                                            uri: appConstants.conf.PAYLOAD_URI,
                                            body: payloadS,
                                            json: true,
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                        };
                                        logger.info("*************************************************")
                                        logger.info("apiOptionsSecondary STEP13 importPortalCheck", apiOptionsSec)
                                        logger.info("*************************************************")
                                            //for portal API URL
                                        apiPromises.push(rp(apiOptionsSec)
                                            .then(apiResponse => {
                                                logger.info(`POST_PORTAL_API Response from API inSecondaryProjectId endpoint  STEP14 importPortalCheck: ${JSON.stringify(apiResponse)}`);
                                                logger.info(`POST_PORTAL_API Response from API endpoint STEP15 importPortalCheck: ${JSON.stringify(apiResponse)}`)
                                                logger.info("PortalapiResponse----------------------------------")

                                                logger.info(apiResponse, "SuccessPortalapiResponse secondary case  STEP16 importPortalCheck")
                                                logger.info("PortalapiResponse----------------------------------")
                                                statusRespresolveonse = {
                                                    status: apiResponse.status,
                                                    message: apiResponse.message,
                                                    data: payloadS,
                                                    apiError: ''
                                                };
                                                responseArray.push(statusRespresolveonse);
                                                logger.info("responseArraystatusObjData  STEP17 importPortalCheck", JSON.stringify(responseArray))

                                                //resolve({ status: apiResponse.status, message: apiResponse.message, data: payload, apiError: '' });

                                                // res.status(200).json({ status: "success", data: payload });
                                            })
                                            .catch(apiError => {
                                                logger.info("PortalapiResponse----------------------------------")

                                                logger.error(apiResponse, "FailPortalapiResponse secondary error case  STEP18 importPortalCheck")
                                                logger.info("PortalapiResponse----------------------------------")
                                                logger.error(`POST_PORTAL_API Error from fail API endpoint  STEP19 importPortalCheck: ${JSON.stringify(apiError)}`);
                                                statusResponse = {
                                                    status: "Failed",
                                                    message: "Fail PortalapiResponse in secondary case",
                                                    data: '',
                                                    apiError: ''
                                                };
                                                responseArray.push(statusResponse);
                                                logger.info(`POST_PORTAL_API Error from fail API endpoint  STEP20 importPortalCheck: ${JSON.stringify(responseArray)}`);

                                                // res.status(200).json({ status: "error", apiError: apiError.message });
                                            }));
                                    }
                                    // console.log("responseArraystatusObjData------------------------", JSON.stringify(responseArray))

                                } else {
                                    logger.info("*************************************************")
                                    logger.info("No matching data!!!  STEP21 importPortalCheck", statusObj)
                                    logger.info("*************************************************")
                                    logger.info(`GET_SCHEDULER_IMPORT_QUERY No response data`);
                                    //  res.status(200).json({ status: "Failed", data: "Success case not found!" });
                                    statusResponse = {
                                        status: "Failed",
                                        message: "No matching data!!!",
                                        data: "",
                                        apiError: ''
                                    };
                                    responseArray.push(statusResponse);

                                    // resolve({ status: "Failed", message: "No matching data!!!", data: "", apiError: '' })

                                }

                            }

                        } catch (error) {
                            logger.error(`GET_SCHEDULER_IMPORT_QUERY Error parsing response  STEP22 importPortalCheck: ${error}`);
                            statusResponse = {
                                status: "Failed",
                                message: "No matching data!!!",
                                data: "",
                                apiError: ''
                            };
                            responseArray.push(statusResponse);
                            logger.info(`GET_SCHEDULER_IMPORT_QUERY responseArray response STEP23 importPortalCheck: ${JSON.stringify(responseArray)}`);
                            // res.status(200).json({ status: "failed", errors: [error.message] });
                            // resolve({ status: "failed", message: "Error parsing response", data: "", apiError: '' })

                        }
                    }
                })
                .catch(graphQLError => {
                    logger.error(`GET_SCHEDULER_IMPORT_QUERY GraphQL Query Error STEP24 importPortalCheck: ${JSON.stringify(graphQLError)}`);
                    statusResponse = {
                        status: "Failed",
                        message: "Failed SCHEDULER_IMPORT_QUERY",
                        data: "",
                        apiError: ''
                    };
                    responseArray.push(statusResponse);
                    logger.info(`GET_SCHEDULER_IMPORT_QUERY responseArray Error response data STEP25importPortalCheck: ${JSON.stringify(responseArray)}`);

                    // res.status(200).json({ status: "failed", errors: [graphQLError.message] });
                    // resolve({ status: "failed", message: "GraphQL Query Error", data: "", apiError: graphQLError })
                }));

        } else {

            const schedulerQuery = buildQuery(inSubmissionId);
            const schedulerOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerQuery);

            apiPromises.push(rp(schedulerOptions)
            .then(response => {
                const responseData = JSON.parse(response);
                const data = JSON.parse(responseData?.data?.getSchedulerImportDetailsBySubmissionUuid);
                console.log("Data for the failed case update###########################################",data);
                logger.info("Data for the failed case update###########################################",data);
                resumedBy = data[0].resumed_by;
                logger.info("*************************************************")
                logger.info("failed load import STEP26 importPortalCheck", isAuditLoad, isMapperLoad)
                logger.info("*************************************************");
    
                let payload = {};
    
                logger.info(`Resumed by from database Failed case::${resumedBy}`)
                logger.info(`userName  from processor Failed::${inPerformedBy}`)
    
                if (isAuditLoad == false || isMapperLoad == false || isInputFileGenerated == false) {
                    payloadP = {
                        inData: {},
                        inProjectId: '',
                        inSource: "SchedulerImport",
                        inAction: "fail",
                        inSubmissionId: inSubmissionId,
                        inSchedulerId: inSchedulerId,
                        inPerformedOn: inPerformedOn,
                        inAssignee: resumedBy ? resumedBy : inPerformedBy,
                        inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                        inDms: inDms,
                        inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                    };
                    payloadS = {
                        inData: {},
                        inProjectId: '',
                        inSource: "SchedulerImport",
                        inAction: "fail",
                        inSubmissionId: inSubmissionId,
                        inSchedulerId: inSchedulerId,
                        inPerformedOn: inPerformedOn,
                        inAssignee: resumedBy ? resumedBy : inPerformedBy,
                        inPerformedBy: resumedBy ? resumedBy : inPerformedBy,
                        inDms: inDms,
                        inCreatedBy: resumedBy ? resumedBy : inPerformedBy
                    };
                    if (isAuditLoad == false) {
                        payloadP.inProcessKey = inProcessKey;
                        payloadS.inProcessKey = inProcessKey;
                    }
                    logger.info("*************************************************")
                    logger.info("fail status import STEP27 importPortalCheck", inProjectId,payloadP,payloadS, inSecondaryProjectId)
                    logger.info("*************************************************")
                    if (inProjectId) {
                        logger.info("*************************************************")
                        logger.info("failedProjectImport STEP27.1 importPortalCheck", inProjectId, inSecondaryProjectId)
                        logger.info("*************************************************")
                        payloadP.inProjectId = inProjectId;
                        payloadP.inActiveBundle = inActiveBundle;
                        const apiOptionsPrimary = {
                            method: 'POST',
                            uri: appConstants.conf.PAYLOAD_URI,
                            body: payloadP,
                            json: true,
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        };
    
                        logger.info("apiOptionsPrimary  STEP27.2 importPortalCheck", apiOptionsPrimary);
                        apiPromises.push(rp(apiOptionsPrimary)
                            .then(apiResponse => {
                                logger.info("*************************************************")
                                logger.info("failedSuccessProjectMapperLoad STEP28 importPortalCheck", apiOptionsPrimary, apiResponse)
                                logger.info("*************************************************");
                                statusResponse = {
                                    status: apiResponse.status,
                                    message: apiResponse.message,
                                    data: payloadP,
                                    apiError: ''
                                };
                                responseArray.push(statusResponse);
                                // resolve({ status: apiResponse.status, data: apiResponse.message, apiError: '' })
                                logger.info(`POST_PORTAL_API Response from Primary Project Fail API endpoint STEP29 importPortalCheck: ${JSON.stringify(apiResponse)}`);
                                // res.status(200).json(apiResponse);
                            })
                            .catch(apiError => {
                                logger.info("*************************************************")
                                logger.info("failed MapperProjectErrApiOptions1 STEP30 importPortalCheck", apiError)
                                logger.info("*************************************************")
                                logger.error(`POST_PORTAL_API Error from  Fail API endpoint STEP31 importPortalCheck: ${apiError}`);
                                statusResponse = {
                                    status: "Failed",
                                    message: "Fail mapper project options in project id case",
                                    data: "",
                                    apiError: ''
                                };
                                responseArray.push(statusResponse);
                                logger.info("***********MapperProjectErrApiOptions1 STEP31.1 importPortalCheck",JSON.stringify(responseArray))
    
                                //res.status(200).json({ status: "error", apiError: apiError.message });
                            }));
                    }
                    if (inSecondaryProjectId) {
                        logger.info("*************************************************")
                        logger.info("failedisSecondaryImport STEP32 importPortalCheck", inProjectId, inSecondaryProjectId)
                        logger.info("*************************************************")
                        payloadS.inProjectId = inSecondaryProjectId;
                        payloadS.inActiveBundle = inSecondaryActiveBundle;
                        const apiOptionsSec = {
                            method: 'POST',
                            uri: appConstants.conf.PAYLOAD_URI,
                            body: payloadS,
                            json: true,
                            headers: {
                                'Content-Type': 'application/json',
                            },
                        };
                        logger.info("apiOptionsSSSSSSSSS STEP33 importPortalCheck", apiOptionsSec);
    
                        apiPromises.push(rp(apiOptionsSec)
                            .then(apiResponse => {
                                logger.info("*************************************************")
                                logger.info("failedSuccessSecondaryProjectMapperLoad STEP34 importPortalCheck", payloadS, apiResponse)
                                logger.info("*************************************************")
                                statusResponse = {
                                    status: apiResponse.status,
                                    message: apiResponse.message,
                                    data: "",
                                    apiError: ''
                                };
                                responseArray.push(statusResponse);
                                //resolve({ status: apiResponse.status, data: apiResponse.message, apiError: '' })
                                logger.info(`POST_PORTAL_API Response from Secondary Project Fail API endpoint STEP35 importPortalCheck: ${JSON.stringify(apiResponse)}`);
                                //res.status(200).json(apiResponse);
                            })
                            .catch(apiError => {
                                logger.info("*************************************************")
                                logger.info("failedMapperSecondaryapiOptions1 STEP36 importPortalCheck", JSON.stringify(apiError))
                                logger.info("*************************************************")
                                logger.error(`POST_PORTAL_API Error from  Fail API endpoint STEP37 importPortalCheck: ${JSON.stringify(apiError)}`);
                                statusResponse = {
                                    status: "error",
                                    message:  "Failed mapper project options in secondary project case",
                                    data: "",
                                    apiError: ''
                                };
                                responseArray.push(statusResponse);
                                logger.info("*********Failed mapper project options in secondary project case STEP37.1 importPortalCheck:",JSON.stringify(responseArray))
    
                                // res.status(200).json({ status: "error", apiError: apiError.message });
    
                            }));
                            logger.info(responseArray, statusResponse, "responseArray---------------")
    
                    };
    
                } else {
    
                }
                logger.info("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
                logger.info(apiPromises , "apiPromises[] STEP38 importPortalCheck");
                logger.info("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
            }))

  

        }
        logger.info(responseArray, "responseArray==================");
        Promise.allSettled(apiPromises)
            .then(() => {
                logger.info("responseArraystatusObjData STEP39 importPortalCheck------------------------", JSON.stringify(responseArray));

                const resp = JSON.stringify(responseArray)
                resolve({ resp });
            })
            .catch(error => {
                // Handle errors from any of the promises
                logger.error(`Error in Promise.all  STEP40 importPortalCheck: ${error}`);
                resolve({ responseArray }); // Resolve with the current responseArray, even if some requests failed
            });
    });

}

routes.use('/', async(req, res) => {
    console.log("req.body&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",req.body);
    req.setTimeout(1800000);
    // const { inSchedulerId: schedulerId, inSubmissionId: inSubmissionUuid, inIsInputFileGenerated, isMapperLoad: inIsMapperLoad, inIsAuditLoad } = req.body;
    const inSchedulerId = req.body.schedulerId; // Ensure you have this in the request body
    const inSubmissionId = req.body.inSubmissionId;
    const inProjectId = req.body.inProjectId;
    const inSecondaryProjectId = req.body.inSecondaryProjectId;
    const isInputFileGenerated = req.body.isInputFileGenerated;
    const isMapperLoad = req.body.isMapperLoad;
    const isAuditLoad = req.body.isAuditLoad;
    const inputFileGeneratedOn = req.body.inputFileGeneratedOn;
    const isImportCompleted = req.body.isImportCompleted;
    const mapperLoadOn = req.body.importCompletedOn;
    const auditLoadOn = req.body.importCompletedOn;
    const inSource = req.body.inSource;
    const inAction = req.body.inAction;
    const inPerformedOn = req.body.inPerformedOn;
    const inPerformedBy = req.body.inPerformedBy;
    const inDms = req.body.inDms;
    const inCreatedBy = req.body.inCreatedBy;
    const companyObj = req.body.companyObj;
    const isInputFileGeneratedComment = req.body.isInputFileGeneratedComment;
    const isMapperLoadComment = req.body.isMapperLoadComment;
    const isAuditLoadComment = req.body.isAuditLoadComment;
    const inProcessKey = req.body.inProcessKey;
    // const inActiveBundle = req.body.inActiveBundle;
    // const inSecondaryActiveBundle = req.body.inSecondaryActiveBundle;
    let companyDetails =  await getCompanyDetails(inSchedulerId);
                                logger.info(`Company details Scheduler import details********************************`,companyDetails);
                                console.log(`Company detail Scheduler import detailss********************************`,companyDetails);
                                isActiveBundle = companyDetails;

    logger.info(inAction, inSource, inDms, inProjectId, inPerformedOn, inPerformedBy, isInputFileGeneratedComment, isMapperLoadComment, isAuditLoadComment,inProcessKey, "requestUriData============")
    if (inAction == "resume") {
        logger.info(inAction, "resume+++++++++++++++++++++++++++++++++++++++++++++++++++")

        try {
            const encKey = Buffer.from(appConstants.conf.PAYLOAD_ENC_KEY).toString('base64');
            let body;
            if (Array.isArray(inProjectId) && inProjectId.length > 0) {
                inProjectId.forEach(projectId => {
                    console.log("Project id for portal call scheduler import>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyDetails[projectId]);
                    console.log("companyDetails[projectId] import>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyDetails[projectId]);
                   let value = isActiveBundle.find(item => item && item.hasOwnProperty(projectId))?.[projectId];
                   console.log("isactive bundle value$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",value);
                    body = {
                        inData: {},
                        inProjectId: projectId,
                        inSource: "SchedulerImport",
                        inAction: "resume",
                        inPerformedOn: inPerformedOn,
                        inAssignee: inPerformedBy,
                        inPerformedBy: inPerformedBy,
                        inDms: inDms,
                        inSubmissionId: "",
						inCreatedBy :inPerformedBy,
                        isActiveBundle:value
                        
                    };

                    const apiOptions = {
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        url: appConstants.conf.PAYLOAD_URI,
                        json: true,
                        body: body
                    };
                    logger.info("=================== resume response::::::::: =========");
                    logger.info(apiOptions, "=================== resume response::::::::: =========");
                    logger.info("=================== resume response::::::::: =========");
                    rp(apiOptions)
                        .then(apiResponse => {
                            console.log(`POST_PORTAL_API Response from Success API endpoint: $ { apiResponse }`);

                            logger.info(`POST_PORTAL_API Response from API endpoint: ${JSON.stringify(apiResponse)}`);
                            res.status(200).send({ status: "success", data: apiResponse });
                        })
                        .catch(apiError => {
                            logger.error(`POST_PORTAL_API Error from Fail API endpoint: ${JSON.stringify(apiError)}`);
                            //res.status(200).send({ status: "error", apiError: apiError.message });
                        });
                });
            } else {
                logger.info(inAction, appConstants.conf.PAYLOAD_URI, inProjectId, "resume2+++++++++++++++++++++++++++++++++++++++++++++++++++")
                console.log("companyDetails[projectId]  elserimport>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyDetails[inProjectId]);
                let value = isActiveBundle.find(item => item && item.hasOwnProperty(inProjectId))?.[inProjectId];
                console.log("isactive bundle value else$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",value);
                body = {
                    inData: {},
                    inProjectId: inProjectId,
                    // inSecondaryProjectId: inSecondaryProjectId,
                    inSource: "SchedulerImport",
                    inAction: "resume",
                    inPerformedOn: inPerformedOn,
                    inAssignee: inPerformedBy,
                    inPerformedBy: inPerformedBy,
                    inCreatedBy:inPerformedBy,
                    inDms: inDms,
                    inSubmissionId: "",
                    isActiveBundle:value
                };
                logger.info(inAction, appConstants.conf.PAYLOAD_URI, "resumeprojectid case+++++++++++++++++++++++++++++++++++++++++++++++++++")

                logger.info(body, "=================== body data::::::::: =========");

                const apiOptions = {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    url: appConstants.conf.PAYLOAD_URI,
                    json: true,
                    body: body
                };

                logger.info(apiOptions, "=================== api data in project id case::::::::: =========");
                rp(apiOptions)
                    .then(apiResponse => {
                        console.log(`POST_PORTAL_API Response from Success API endpoint: ${JSON.stringify(apiResponse)}`);

                        logger.info(`POST_PORTAL_API Response from API endpoint: ${JSON.stringify(apiResponse)}`);
                        res.status(200).send({ status: "success", data: apiResponse });
                    })
                    .catch(apiError => {
                        logger.error(`POST_PORTAL_API Error from Fail API endpoint: ${apiError}`);
                        //res.status(200).send({ status: "error", apiError: apiError.message });
                    });
            }

            // request.post(options, (err, res, body) => {
            //     if (err) {
            //         console.log(err);
            //         segment.saveSegment(`doPayloadAction - Error: ${JSON.stringify(err)}`);
            //     }
            //     try {
            //         segment.saveSegment(`doPayloadAction - Status: ${res.statusCode}`);
            //         res.status(200).send({ status: "success", data: res });
            //         console.log(`ResponseStatus: ${res.statusCode}`);
            //         segment.saveSegment(`doPayloadAction - body: ${JSON.stringify(body)}`);
            //         console.log(body, "OP BODY FROM SERVER");
            //     } catch (err) {
            //         segment.saveSegment(`doPayloadAction Error - ${JSON.stringify(err)}`);
            //     }

            // });
        } catch (error) {
            console.log(`Error: ${error}`);
            logger.error(`Error: ${error}`);
            segment.saveSegment(`doPayloadAction Error - ${JSON.stringify(error)}`);
        }
        // console.log(inAction, "inAction---------------------")
        //     // Check if is_input_file_generated, is_mapper_load, and is_audit_load are true
        // let payload;
        // payload = {
        //     // status: "success",
        //     inData: {},
        //     inProjectId: inProjectId,
        //     inSource: "SchedulerImport",
        //     inAction: "resume",
        //     inPerformedOn: inPerformedOn,
        //     inPerformedBy: inPerformedBy,
        //     inDms: inDms,
        // };
        // const apiOptions = {
        //     method: 'POST',
        //     uri: appConstants.conf.PAYLOAD_URI,
        //     body: payload,
        //     json: true,
        //     headers: {
        //         'Content-Type': 'application/json',
        //         'authKey': encKey
        //     },
        // };
        //for portal API URL

    } else {
        if (!inSubmissionId) {
            return res.status(200).send({ status: "failed", message: "Input Missing!" });
        }
        //For update scheduler import
        let schedulerImportQuery;
        if (typeof isInputFileGenerated === 'boolean') {
            schedulerImportQuery = buildSchedulerImportQuery(inSubmissionId, isInputFileGenerated, null, null, isInputFileGeneratedComment,
                null, null);
                logger.info("schedulerImportQueryInputTestingggggggggg==============================", isInputFileGenerated);
        } else if (typeof isMapperLoad === 'boolean') {
            schedulerImportQuery = buildSchedulerImportQuery(inSubmissionId, null, isMapperLoad, null, null,
                isMapperLoadComment, null);
                logger.info("schedulerImportQueryMapperTestingggggggggg==============================", isMapperLoad);
        } else if (typeof isAuditLoad === 'boolean') { // Assuming isAuditLoad can be a non-boolean but truthy value
            schedulerImportQuery = buildSchedulerImportQuery(inSubmissionId, null, null, isAuditLoad, null,
                null, isAuditLoadComment);
                logger.info("schedulerImportQueryAuditTestingggggggggg==============================", isAuditLoad);
        }

        const schedulerImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerImportQuery);
        logger.info("=================================================")
        logger.info(schedulerImportOptions, schedulerImportQuery, "updatestatus1.0==============")
        logger.info("=================================================")
        rp(schedulerImportOptions)
            .then(async(response) => {
                if (!response) {
                    logger.error(`UPDATE_SCHEDULER_IMPORT_QUERY No response data from GraphQL query`);
                    return res.status(200).json({
                        status: "failed",
                        message: `No response from GraphQL query:
                        ${response}`
                    });
                }
                logger.info("=================================================")
                logger.info(JSON.parse(response), " No response from GraphQL query: updatestatus1.1==============")
                logger.info("=================================================")

                try {
                    const responseData = JSON.parse(response);
                    const parsedData = JSON.parse(responseData.data.updateSchedulerImportDetails.json);
                    const { status } = parsedData;
                    if (status === "success") {
                        logger.info(`ResponseStatus:${responseData,parsedData}`);
                        logger.info(`UPDATE_SCHEDULER_IMPORT_QUERY Successfully updated updatestatus1.2: ${JSON.stringify(response)}`);
                        /**  ********************Selection status  ************************ */
                        const responseStatus = await getStatusFromSchedulerDB(isInputFileGenerated, isMapperLoad, isAuditLoad, inSubmissionId, inPerformedBy, inProjectId, inSchedulerId, inSource, inAction, inDms, inCreatedBy, inPerformedOn, inSecondaryProjectId,inProcessKey);
                        logger.info("COMPLETED UPDATE_SCHEDULER_IMPORT_QUERY updatestatus1.11", responseStatus);
                        // Parse the JSON string
                        const parsedResponse = JSON.parse(responseStatus.resp);

                        // Process each item
                        parsedResponse.forEach((item) => {
                            logger.info('Status:', item.status);
                            logger.info('Message:', item.message);
                        });
                        logger.info("COMPLETED UPDATE_SCHEDULER_IMPORT_QUERY DATA RESULT updatestatus1.3", parsedResponse[0]);

                        res.status(200).json({ status: parsedResponse[0].status, message: parsedResponse[0].message });
                        /** ************************End Section *************************** */


                    } else {
                        logger.error(`UPDATE_SCHEDULER_IMPORT_QUERY Error response status updatestatus1.4: ${status}`);
                        res.status(200).json({ status: "failed", message: `UPDATE_SCHEDULER_IMPORT_QUERY Error response status: ${status}` });
                    }
                } catch (error) {
                    logger.error(`UPDATE_SCHEDULER_IMPORT_QUERY Error parsing response updatestatus1.5: ${error}`);
                    // res.status(200).send(`Error parsing response: ${error}`);
                }
            })
            .catch(error => {
                logger.error(`UPDATE_SCHEDULER_IMPORT_QUERY Error parsing response updatestatus1.6: ${error}`);
                // res.status(200).json({ status: "failed", errors: [error.message] });
            });

    }

});

module.exports = routes;